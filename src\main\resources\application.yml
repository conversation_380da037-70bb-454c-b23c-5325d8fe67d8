server:
  port: 9200

spring:
  application:
    name: icm
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  servlet:
    multipart:
      max-file-size: 1024MB
      max-request-size: 1024MB

# 用户配置文件路径
user:
  config:
    path: ${USER_CONFIG_PATH:/home/<USER>/icm/config/users.yml}

# 升级功能配置
upgrade:
  storage:
    directory: ${UPGRADE_STORAGE_DIR:/home/<USER>/icm/upgrade}
    #directory: ${UPGRADE_STORAGE_DIR:C:/home/<USER>/icm/upgrade}
  file:
    packages: ${upgrade.storage.directory}/packages.json
    tasks: ${upgrade.storage.directory}/tasks.json
    previous: ${upgrade.storage.directory}/previous.json
    logs: ${upgrade.storage.directory}/upgrade_logs.json

# 网关服务配置
gateway:
  # 控制是否执行updateGateway方法更新网关配置
  update:
    enabled: true

# x-file-storage 文件存储配置
dromara:
  x-file-storage:
    default-platform: minio-1
    thumbnail-suffix: ".min.jpg"
    minio:
      - platform: minio-1
        enable-storage: true
        access-key: ${MINIO_ACCESS_KEY:admin}
        secret-key: ${MINIO_SECRET_KEY:Jykj1994@}
        end-point: ${MINIO_ENDPOINT:http://localhost:9001}
        bucket-name: ${MINIO_BUCKET:icm-files}
        domain: /

# Knife4j配置
knife4j:
  enable: true
  basic:
    enable: false
    username: admin
    password: 123456

secretKey: R#b9K!m2@Y7G%f3P6g^Q2w#M9E!r4C

# JsonDb配置
json:
  db:
    windows:
      path: C:/home/<USER>/icm/data.json
    linux:
      path: /home/<USER>/icm/data.json

logging:
  file:
    name: ${LOG_FILE:/var/log/icm/icm.log}
  level:
    root: INFO
    com.opengauss.monitor: ${LOG_LEVEL:INFO}