# OpenGauss Docker 初始化脚本说明

## 概述

本目录包含了用于在 Docker 容器启动时自动执行的 OpenGauss 数据库初始化脚本。

## 文件说明

### `init-user.sql`

- **功能**: 自动创建 `netguard` 用户和 `netguard_db` 数据库
- **执行时机**: Docker 容器首次启动时自动执行
- **创建的用户**: `netguard`，密码为 `fsTg6fB2u@admin`
- **创建的数据库**: `netguard_db`，所有者为 `netguard`

## 使用方式

### 自动部署（推荐）

当您通过 ICM 系统部署 OpenGauss 服务时，系统会自动：

1. 将初始化脚本复制到 `/home/<USER>/icm/init/` 目录
2. 在启动 Docker 容器时挂载该目录到容器的 `/docker-entrypoint-initdb.d/`
3. OpenGauss 容器启动时会自动执行该目录中的所有 `.sql` 文件

### 手动部署

如果您需要手动部署，可以按以下步骤操作：

1. **确保脚本文件存在**

   ```bash
   mkdir -p /home/<USER>/icm/init
   # 将 init-user.sql 复制到该目录
   ```

2. **启动容器时挂载初始化目录**
   ```bash
   docker run --network=host --restart=always --privileged=true \
     --name opengauss -h opengauss -d \
     -e GS_PORT=5432 -e OG_SUBNET=bridge \
     -e GS_PASSWORD=您的数据库密码 \
     -e NODE_NAME=opengauss \
     -v /home/<USER>/gauss5:/var/lib/opengauss \
     -v /home/<USER>/icm/init:/docker-entrypoint-initdb.d \
     enmotech/opengauss:5.0.3 -M primary
   ```

## 验证初始化结果

容器启动后，您可以通过以下方式验证初始化是否成功：

```bash
# 连接到数据库
docker exec -it opengauss gsql -h localhost -U netguard -d netguard_db -W

# 或者使用超级用户检查
docker exec -it opengauss gsql -h localhost -U gaussdb -d postgres -c "\\l"
```

## 自定义配置

如果您需要修改用户名、密码或数据库名，请：

1. 修改 `init-user.sql` 文件中的相应内容
2. 重新部署服务以应用更改

## 注意事项

1. **首次启动**: 初始化脚本只在容器首次启动时执行
2. **数据持久化**: 如果容器的数据目录已存在数据，初始化脚本将不会执行
3. **权限**: 创建的用户具有对指定数据库的完全访问权限
4. **安全**: 请根据实际环境修改默认密码

## 故障排除

### 脚本未执行

- 检查容器是否为首次启动
- 确认脚本文件路径和挂载是否正确
- 查看容器日志：`docker logs opengauss`

### 用户创建失败

- 检查脚本语法是否正确
- 确认用户名不与现有用户冲突
- 查看数据库错误日志
