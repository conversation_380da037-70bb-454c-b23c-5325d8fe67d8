package com.jykj.icm.service;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.nio.file.StandardOpenOption;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Enumeration;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Properties;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.RedisConnectionFailureException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisSentinelConnection;
import org.springframework.data.redis.connection.RedisServer;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import com.jykj.icm.common.Constants;
import com.jykj.icm.common.exception.BusinessException;
import com.jykj.icm.config.MinioInitializer;
import com.jykj.icm.entity.HostInfo;
import com.jykj.icm.entity.HostInfoDTO;
import com.jykj.icm.entity.HostInfoTestDTO;
import com.jykj.icm.entity.ServiceRequest;
import com.jykj.icm.entity.UpdateGaussPasswordRequest;
import com.jykj.icm.entity.UpdateVipRequest;
import com.jykj.icm.listener.RedisMessageListener;
import com.jykj.icm.utils.AESUtils;
import com.jykj.icm.utils.ExecuteUtils;
import com.jykj.icm.utils.NetworkUtils;
import com.jykj.icm.utils.PasswordUtils;
import com.jykj.icm.utils.RedisUtils;
import com.jykj.icm.utils.StringUtils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrFormatter;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import io.lettuce.core.RedisConnectionException;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class HostConfigServer {
    private final JsonDbManager jsonDbsonManager;
    private final RedisUtils redisUtils;
    private final MinioInitializer minioInitializer;

    @Lazy
    @Autowired
    private RedisMessageListener redisMessageListener;

    @Value("${server.port:9200}")
    private String serverPort;

    @Value("${secretKey}")
    private String secretKey;

    @Value("${gateway.update.enabled:false}")
    private boolean gatewayUpdateEnabled;

    @Value("${dromara.x-file-storage.minio[0].access-key}")
    private String minioAccessKey;

    @Value("${dromara.x-file-storage.minio[0].secret-key}")
    private String minioSecretKey;

    // 创建专用线程池（根据实际情况配置参数）
    private static final ExecutorService CHECK_POOL = Executors.newCachedThreadPool();

    public void addHostConfig(HostInfo hostInfo) {
        initializeHostInfo(hostInfo);
        if (Constants.HOST_TYPE_SLAVE.equals(hostInfo.getHostType())) {
            addMasterInfo(hostInfo);
        }
        Map<String, Object> map = BeanUtil.beanToMap(hostInfo);
        jsonDbsonManager.addRow(map);
        if (Constants.HOST_TYPE_MASTER.equals(hostInfo.getHostType())) {
            deployMasterServices(hostInfo);
        } else if (Constants.HOST_TYPE_SLAVE.equals(hostInfo.getHostType())) {
            deploySlaveServices(hostInfo);
        }
        addInfoToRedis(hostInfo, false);
    }

    private void addInfoToRedis(HostInfo hostInfo, boolean needResetSubscription) {
        // 异步等待 Redis 启动并写入配置
        CompletableFuture.runAsync(() -> {
            if (Constants.HOST_TYPE_MASTER.equals(hostInfo.getHostType())) {
                redisUtils.redisSet(hostInfo.getLocalIp(), Constants.REDIS_HOSTINFO_KEY + "::" + hostInfo.getLocalIp(),
                        JSONUtil.toJsonStr(hostInfo));
                redisUtils.redisSet(hostInfo.getLocalIp(), Constants.REDIS_MASTER_IP_KEY, hostInfo.getLocalIp());
            } else {
                redisUtils.redisSet(hostInfo.getRemoteIp(), Constants.REDIS_HOSTINFO_KEY + "::" + hostInfo.getLocalIp(),
                        JSONUtil.toJsonStr(hostInfo));
                redisUtils.redisSet(hostInfo.getRemoteIp(), Constants.REDIS_MASTER_IP_KEY, hostInfo.getLocalIp());
            }
            // 如果主机类型发生变化，重置订阅状态
            if (needResetSubscription) {
                redisMessageListener.resetSubscription();
            }
        });
    }

    private void initializeHostInfo(HostInfo hostInfo) {
        if (StrUtil.isBlank(hostInfo.getId())) {
            hostInfo.setId(IdUtil.getSnowflakeNextIdStr());
        }
        encryptPasswordFields(hostInfo);
    }

    private void encryptPasswordFields(HostInfo hostInfo) {
        if (StrUtil.isNotBlank(hostInfo.getPassword())) {
            hostInfo.setPassword(AESUtils.isAESEnCode(hostInfo.getPassword()) ? hostInfo.getPassword()
                    : AESUtils.AESEnCode(hostInfo.getPassword()));
        }
        if (StrUtil.isNotBlank(hostInfo.getDbPassword())) {
            hostInfo.setDbPassword(AESUtils.isAESEnCode(hostInfo.getDbPassword()) ? hostInfo.getDbPassword()
                    : AESUtils.AESEnCode(hostInfo.getDbPassword()));
        }
    }

    private void deployMasterServices(HostInfo hostInfo) {
        try {
            log.info("开始主服务部署：{}", hostInfo);
            log.info("开始主服务器创建相关目录----------");
            createRequiredDirectories();
            log.info("开始主服务器部署Redis服务----------");
            deployRedisMaster(hostInfo);
            log.info("开始主服务器部署Sentinel服务----------");
            deploySentinel(hostInfo);
            List<String> list = getHostKeyList(hostInfo);
            log.info("开始主服务器部署Gauss服务----------");
            deployGaussMaster(hostInfo, list);
            log.info("开始主服务器部署Keepalived服务----------");
            deployKeepalivedMaster(hostInfo);
            log.info("开始主服务器部署Nginx服务----------");
            deployNginxMaster(hostInfo, list);
            // 更新sso服务,主要是数据库url和redis配置
            log.info("开始主服务器部署sso服务----------");
            updateSso(hostInfo, list);
            // 更新网关的服务
            log.info("开始主服务器部署网关服务----------");
            updateGateway(hostInfo, list);
            // minio 服务
            log.info("开始主服务器部署minio服务----------");
            updateMinioMaster(hostInfo, list);
        } catch (Exception e) {
            throw new RuntimeException("主服务部署失败: " + e.getMessage(), e);
        }
    }

    @SneakyThrows
    private void updateMinioMaster(HostInfo hostInfo, List<String> list) {
        // 使用配置文件中的认证信息启动MinIO容器
        List<String> dockerContainer = getAllDockerContainer();
        log.info("存在的容器：{}", dockerContainer);
        if (dockerContainer.contains("minio")) {
            executeCommand("docker", "rm", "-f", "minio");
        }
        executeCommand("docker", "run", "--network=host", "--name", "minio", "-d", "--restart=always",
                "-e", "MINIO_ACCESS_KEY=" + minioAccessKey, "-e", "MINIO_SECRET_KEY=" + minioSecretKey,
                "-v", "/home/<USER>/data:/data", "minio/minio", "server", "/data",
                "--console-address", ":9002", "-address", ":9001");

        // 初始化MinIO bucket（使用配置文件中的认证信息）
        String minioEndpoint = "http://" + hostInfo.getLocalIp() + ":9001";
        minioInitializer.initializeBucketWithCustomEndpoint(minioEndpoint);
    }

    /**
     * 更新网关服务的所有配置
     */
    public void updateGateway(HostInfo hostInfo, List<String> list) {
        // 检查是否启用网关更新功能
        if (!gatewayUpdateEnabled) {
            log.info("网关服务配置更新功能已禁用，跳过更新");
            return;
        }

        try {
            log.info("开始更新网关服务配置");

            // 如果列表为空，获取主机列表
            if (CollUtil.isEmpty(list)) {
                list = getHostKeyList(hostInfo);
            }

            if (CollUtil.isEmpty(list)) {
                log.info("没有从redis中获取到主机信息，无法更新网关服务");
                return;
            }

            // 1. 更新 vpn_server_v2/config.yml 文件
            updateVpnServerConfig(list);

            // 2. 更新 vpn_manage_v2/config.json 文件
            updateVpnManageConfig(hostInfo);

            // 3. 更新 vpn_manage_v2/dbconfig.yml 文件
            updateVpnManageDbConfig(hostInfo, list);

            // 4. 提供系统启动命令功能实现
            setupServiceCommands();

            log.info("网关服务配置更新完成");
        } catch (Exception e) {
            log.error("更新网关服务配置失败", e);
            throw new RuntimeException("更新网关服务配置失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新 vpn_server_v2/config.yml 文件 主要更新 Redis 和 PostgreSQL 配置
     */
    @SneakyThrows
    private void updateVpnServerConfig(List<String> list) {
        log.info("更新 vpn_server_v2/config.yml 配置文件，只修改红框内配置项");

        String configPath = "/usr/local/soft/vpn_server_v2/config.yml";
        Path path = Paths.get(configPath);

        // 检查文件是否存在，不存在则创建
        if (!Files.exists(path)) {
            Files.createDirectories(path.getParent());
            // 创建初始配置文件内容
            StringBuilder initialConfig = new StringBuilder();
            initialConfig.append("redis:").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  host: 127.0.0.1").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  port: 6379").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  password: " + Constants.DEFAULT_PASSWORD).append(Constants.LINE_SEPARATOR);
            initialConfig.append("  db: 0").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  sentinel:").append(Constants.LINE_SEPARATOR);
            initialConfig.append("    enabled: true").append(Constants.LINE_SEPARATOR);
            initialConfig.append("    masterName: \"mymaster\"").append(Constants.LINE_SEPARATOR);
            initialConfig.append("    nodes:").append(Constants.LINE_SEPARATOR);
            initialConfig.append("      - \"127.0.0.1:26379\"").append(Constants.LINE_SEPARATOR);
            initialConfig.append("    sentinelPassword: \"" + Constants.DEFAULT_PASSWORD + "\"")
                    .append(Constants.LINE_SEPARATOR);
            initialConfig.append(Constants.LINE_SEPARATOR);
            initialConfig.append("postgresql:").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  host: 127.0.0.1").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  port: \"5432\"").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  user: netguard").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  password: fsTg6fB2u@admin").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  sslMode: disable").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  dbName: netguard_db").append(Constants.LINE_SEPARATOR);
            initialConfig.append(Constants.LINE_SEPARATOR);
            initialConfig.append("ipwhitelist:").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  - 127.0.0.1").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  - **********").append(Constants.LINE_SEPARATOR);
            initialConfig.append(Constants.LINE_SEPARATOR);
            initialConfig.append("ssoLogin:").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  url: https://127.0.0.1:9000").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  systemId: NetGuard").append(Constants.LINE_SEPARATOR);
            initialConfig.append(Constants.LINE_SEPARATOR);
            initialConfig.append("lynxServer:").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  orgId: \"1234454\"").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  customerName: \"南充市中心医院测试\"").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  lynxIp: https://***********").append(Constants.LINE_SEPARATOR);
            initialConfig.append(Constants.LINE_SEPARATOR);
            initialConfig.append("netGuard:").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  port: \"50120\"").append(Constants.LINE_SEPARATOR);

            Files.write(path, initialConfig.toString().getBytes());
        }

        // 备份原文件
        Files.copy(path, path.resolveSibling("config.yml.bak"), StandardCopyOption.REPLACE_EXISTING);

        String content = new String(Files.readAllBytes(path));

        // 构建Redis Sentinel节点列表
        StringBuilder sentinelNodes = new StringBuilder();
        for (String ip : list) {
            String normalIp = ip.substring(Constants.REDIS_HOSTINFO_KEY.length() + 2);
            sentinelNodes.append("      - \"").append(normalIp).append(":26379\"").append(Constants.LINE_SEPARATOR);
        }

        // 构建PostgreSQL节点列表
        StringBuilder pgHosts = new StringBuilder();
        StringBuilder pgPorts = new StringBuilder();
        for (String ip : list) {
            String normalIp = ip.substring(Constants.REDIS_HOSTINFO_KEY.length() + 2);
            if (pgHosts.length() > 0) {
                pgHosts.append(",");
                pgPorts.append(",");
            }
            pgHosts.append(normalIp);
            pgPorts.append("5432");
        }

        // 只替换redis和postgresql红框内内容
        // 1. 替换redis配置 - 使用更精确的替换方式
        // 找到redis部分的开始和结束位置
        int redisStart = content.indexOf("\nredis:");
        if (redisStart == -1) {
            // 如果在行首，直接查找
            if (content.startsWith("redis:")) {
                redisStart = 0;
            }
        } else {
            redisStart += 1; // 跳过换行符
        }
        int redisEndIndex = content.indexOf("\npostgresql:", redisStart);
        if (redisEndIndex == -1) {
            redisEndIndex = content.indexOf("\nipwhitelist:", redisStart);
        }
        if (redisEndIndex == -1) {
            redisEndIndex = content.indexOf("\nssoLogin:", redisStart);
        }
        if (redisEndIndex == -1) {
            redisEndIndex = content.indexOf("\nlynxServer:", redisStart);
        }
        if (redisEndIndex == -1) {
            redisEndIndex = content.indexOf("\nnetGuard:", redisStart);
        }

        if (redisStart >= 0 && redisEndIndex >= 0) {
            String beforeRedis = content.substring(0, redisStart);
            String afterRedis = content.substring(redisEndIndex);

            // 构建新的redis配置
            StringBuilder newRedisConfig = new StringBuilder();
            newRedisConfig.append("redis:").append(Constants.LINE_SEPARATOR);
            newRedisConfig.append("  host: 127.0.0.1").append(Constants.LINE_SEPARATOR);
            newRedisConfig.append("  port: 6379").append(Constants.LINE_SEPARATOR);
            newRedisConfig.append("  password: \"").append(Constants.DEFAULT_PASSWORD).append("\"")
                    .append(Constants.LINE_SEPARATOR);
            newRedisConfig.append("  db: 0").append(Constants.LINE_SEPARATOR);
            newRedisConfig.append("  sentinel:").append(Constants.LINE_SEPARATOR);
            newRedisConfig.append("    enabled: true").append(Constants.LINE_SEPARATOR);
            newRedisConfig.append("    masterName: \"mymaster\"").append(Constants.LINE_SEPARATOR);
            newRedisConfig.append("    nodes:").append(Constants.LINE_SEPARATOR);
            newRedisConfig.append(sentinelNodes);
            newRedisConfig.append("    sentinelPassword: \"").append(Constants.DEFAULT_PASSWORD).append("\"")
                    .append(Constants.LINE_SEPARATOR);
            newRedisConfig.append(Constants.LINE_SEPARATOR);

            content = beforeRedis + newRedisConfig.toString() + afterRedis;
        }

        // 2. 替换postgresql配置 - 使用更精确的替换方式
        // 找到postgresql部分的开始和结束位置 - 查找以postgresql:开头的行
        int pgStart = content.indexOf("\npostgresql:");
        if (pgStart == -1) {
            // 如果在行首，直接查找
            if (content.startsWith("postgresql:")) {
                pgStart = 0;
            }
        } else {
            pgStart += 1; // 跳过换行符
        }
        int pgEndIndex = content.indexOf("\nipwhitelist:", pgStart);
        if (pgEndIndex == -1) {
            pgEndIndex = content.indexOf("\nssoLogin:", pgStart);
        }
        if (pgEndIndex == -1) {
            pgEndIndex = content.indexOf("\nlynxServer:", pgStart);
        }
        if (pgEndIndex == -1) {
            pgEndIndex = content.indexOf("\nnetGuard:", pgStart);
        }

        if (pgStart >= 0 && pgEndIndex >= 0) {
            String beforePg = content.substring(0, pgStart);
            String afterPg = content.substring(pgEndIndex);

            // 构建新的postgresql配置
            StringBuilder newPgConfig = new StringBuilder();
            newPgConfig.append("postgresql:").append(Constants.LINE_SEPARATOR);
            newPgConfig.append("  host: ").append(pgHosts).append(Constants.LINE_SEPARATOR);
            newPgConfig.append("  port: \"").append(pgPorts).append("\"").append(Constants.LINE_SEPARATOR);
            newPgConfig.append("  user: netguard").append(Constants.LINE_SEPARATOR);
            newPgConfig.append("  password: fsTg6fB2u@admin").append(Constants.LINE_SEPARATOR);
            newPgConfig.append("  sslMode: disable").append(Constants.LINE_SEPARATOR);
            newPgConfig.append("  dbName: netguard_db").append(Constants.LINE_SEPARATOR);
            newPgConfig.append(Constants.LINE_SEPARATOR);

            content = beforePg + newPgConfig.toString() + afterPg;
        }

        Files.write(path, content.getBytes());
        log.info("vpn_server_v2/config.yml 红框内配置项更新成功");
    }

    /**
     * 更新 vpn_manage_v2/config.json 文件 主要更新 host (主机IP) 和 wg_device（网卡名称）
     */
    @SneakyThrows
    private void updateVpnManageConfig(HostInfo hostInfo) {
        log.info("更新 vpn_manage_v2/config.json 配置文件");

        String configPath = "/usr/local/soft/vpn_manage_v2/config.json";
        Path path = Paths.get(configPath);

        // 检查文件是否存在，不存在则创建
        if (!Files.exists(path)) {
            Files.createDirectories(path.getParent());
            // 创建初始配置文件，使用StringBuilder和Constants.LINE_SEPARATOR
            StringBuilder initialConfig = new StringBuilder();
            initialConfig.append("{").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  \"version\": \"1\",").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  \"host\": \"127.0.0.1\",").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  \"port\": 50121,").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  \"auto_ssl\": false,").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  \"password\": \"netguard\",").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  \"lang\": \"en\",").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  \"log_level\": \"error\",").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  \"wg_private_key\": \"YK/3Twt8G5RxALVNSz2V2aVH1xC0iE4RDaCS0u+LWX8=\",")
                    .append(Constants.LINE_SEPARATOR);
            initialConfig.append("  \"wg_device\": \"enp125s0f0\",").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  \"wg_port\": 50120,").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  \"wg_mtu\": 1280,").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  \"wg_persistent_keepalive\": 25,").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  \"wg_address\": \"**********/16\",").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  \"wg_dns\": \"*******\",").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  \"wg_allowed_ips\": \"0.0.0.0/0, ::/0\"").append(Constants.LINE_SEPARATOR);
            initialConfig.append("}");

            Files.write(path, initialConfig.toString().getBytes());
        }

        // 备份原文件
        Files.copy(path, path.resolveSibling("config.json.bak"), StandardCopyOption.REPLACE_EXISTING);

        // 读取原配置
        String content = new String(Files.readAllBytes(path));

        // 转换为JSON对象，进行修改
        JSONObject jsonObject = JSONUtil.parseObj(content);

        // 更新 host (主机IP)
        jsonObject.put("host", hostInfo.getLocalIp());

        // 更新 wg_device (网卡名称)
        jsonObject.put("wg_device", hostInfo.getNetworkCard());

        // 将更新后的JSON写回文件
        Files.write(path, JSONUtil.toJsonPrettyStr(jsonObject).getBytes());
        log.info("vpn_manage_v2/config.json 配置文件更新成功");
    }

    /**
     * 更新 vpn_manage_v2/dbconfig.yml 文件 主要更新 Redis 和 DB 参数
     */
    @SneakyThrows
    private void updateVpnManageDbConfig(HostInfo hostInfo, List<String> list) {
        log.info("更新 vpn_manage_v2/dbconfig.yml 配置文件，只修改红框内配置项");

        String configPath = "/usr/local/soft/vpn_manage_v2/dbconfig.yml";
        Path path = Paths.get(configPath);

        // 检查文件是否存在，不存在则创建
        if (!Files.exists(path)) {
            Files.createDirectories(path.getParent());
            // 创建初始配置文件
            StringBuilder initialConfig = new StringBuilder();
            initialConfig.append("db:").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  host: 127.0.0.1").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  port: \"5432\"").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  user: netguard").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  password: fsTg6fB2u@admin").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  sslMode: disable").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  dbName: netguard_db").append(Constants.LINE_SEPARATOR);
            initialConfig.append(Constants.LINE_SEPARATOR);
            initialConfig.append("redis:").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  host: 127.0.0.1").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  port: 6379").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  password: " + Constants.DEFAULT_PASSWORD).append(Constants.LINE_SEPARATOR);
            initialConfig.append("  db: 0").append(Constants.LINE_SEPARATOR);
            initialConfig.append("  sentinel:").append(Constants.LINE_SEPARATOR);
            initialConfig.append("    enabled: true").append(Constants.LINE_SEPARATOR);
            initialConfig.append("    masterName: \"mymaster\"").append(Constants.LINE_SEPARATOR);
            initialConfig.append("    nodes:").append(Constants.LINE_SEPARATOR);
            initialConfig.append("      - \"127.0.0.1:26379\"").append(Constants.LINE_SEPARATOR);
            initialConfig.append("    sentinelPassword: \"" + Constants.DEFAULT_PASSWORD + "\"")
                    .append(Constants.LINE_SEPARATOR);

            Files.write(path, initialConfig.toString().getBytes());
        }

        // 备份原文件
        Files.copy(path, path.resolveSibling("dbconfig.yml.bak"), StandardCopyOption.REPLACE_EXISTING);

        // 读取原配置
        String content = new String(Files.readAllBytes(path));

        // 构建PostgreSQL节点列表
        StringBuilder pgHosts = new StringBuilder();
        StringBuilder pgPorts = new StringBuilder();
        for (String ip : list) {
            String normalIp = ip.substring(Constants.REDIS_HOSTINFO_KEY.length() + 2);
            if (pgHosts.length() > 0) {
                pgHosts.append(",");
                pgPorts.append(",");
            }
            pgHosts.append(normalIp);
            pgPorts.append("5432");
        }

        // 构建Redis Sentinel节点列表
        StringBuilder sentinelNodes = new StringBuilder();
        for (String ip : list) {
            String normalIp = ip.substring(Constants.REDIS_HOSTINFO_KEY.length() + 2);
            sentinelNodes.append("      - \"").append(normalIp).append(":26379\"").append(Constants.LINE_SEPARATOR);
        }

        // 只替换db和redis red框内内容
        // 1. 替换db配置 - 使用更精确的替换方式
        // 找到db部分的开始和结束位置
        int dbStart = content.indexOf("db:");
        int dbEnd = content.indexOf("redis:", dbStart);
        if (dbStart >= 0 && dbEnd >= 0) {
            String beforeDb = content.substring(0, dbStart);
            String afterRedis = content.substring(dbEnd);

            // 构建新的db配置
            StringBuilder newDbConfig = new StringBuilder();
            newDbConfig.append("db:").append(Constants.LINE_SEPARATOR);
            newDbConfig.append("  host: ").append(pgHosts).append(Constants.LINE_SEPARATOR);
            newDbConfig.append("  port: \"").append(pgPorts).append("\"").append(Constants.LINE_SEPARATOR);
            newDbConfig.append("  user: netguard").append(Constants.LINE_SEPARATOR);
            newDbConfig.append("  password: fsTg6fB2u@admin").append(Constants.LINE_SEPARATOR);
            newDbConfig.append("  sslMode: disable").append(Constants.LINE_SEPARATOR);
            newDbConfig.append("  dbName: netguard_db").append(Constants.LINE_SEPARATOR);
            newDbConfig.append(Constants.LINE_SEPARATOR);

            content = beforeDb + newDbConfig.toString() + afterRedis;
        }

        // 2. 替换redis配置 - 使用更精确的替换方式
        // 找到redis部分的开始和结束位置
        int redisStart = content.indexOf("redis:");
        int redisEnd = content.length(); // 默认到文件末尾

        // 查找redis配置后的下一个配置块来确定结束位置
        String[] nextSections = { "\nsecurityConfig:", "\nlogging:", "\nserver:", "\nspring:" };
        for (String section : nextSections) {
            int sectionIndex = content.indexOf(section, redisStart);
            if (sectionIndex != -1 && sectionIndex < redisEnd) {
                redisEnd = sectionIndex;
            }
        }

        if (redisStart >= 0) {
            String beforeRedis = content.substring(0, redisStart);
            String afterRedis = content.substring(redisEnd);

            // 构建新的redis配置
            StringBuilder newRedisConfig = new StringBuilder();
            newRedisConfig.append("redis:").append(Constants.LINE_SEPARATOR);
            newRedisConfig.append("  host: 127.0.0.1").append(Constants.LINE_SEPARATOR);
            newRedisConfig.append("  port: 6379").append(Constants.LINE_SEPARATOR);
            newRedisConfig.append("  password: \"").append(Constants.DEFAULT_PASSWORD).append("\"")
                    .append(Constants.LINE_SEPARATOR);
            newRedisConfig.append("  db: 0").append(Constants.LINE_SEPARATOR);
            newRedisConfig.append("  sentinel:").append(Constants.LINE_SEPARATOR);
            newRedisConfig.append("    enabled: true").append(Constants.LINE_SEPARATOR);
            newRedisConfig.append("    masterName: \"mymaster\"").append(Constants.LINE_SEPARATOR);
            newRedisConfig.append("    nodes:").append(Constants.LINE_SEPARATOR);
            newRedisConfig.append(sentinelNodes);
            newRedisConfig.append("    sentinelPassword: \"").append(Constants.DEFAULT_PASSWORD).append("\"")
                    .append(Constants.LINE_SEPARATOR);
            if (!afterRedis.startsWith("\n")) {
                newRedisConfig.append(Constants.LINE_SEPARATOR);
            }

            content = beforeRedis + newRedisConfig.toString() + afterRedis;
        }

        // 写入更新后的配置
        Files.write(path, content.getBytes());
        log.info("vpn_manage_v2/dbconfig.yml 红框内配置项更新成功");
    }

    /**
     * 设置系统启动命令 创建服务脚本，实现启动、停止、重启功能
     */
    @SneakyThrows
    private void setupServiceCommands() {
        // 执行sh /usr/local/soft/services.sh restart 重启
        ExecuteUtils.executeCommand("sudo", "sh", "/usr/local/soft/services.sh", "restart");
    }

    public List<String> getHostKeyList(HostInfo hostInfo) {
        List<String> list;
        Object o = redisUtils.redisGetKey(hostInfo.getLocalIp(), Constants.REDIS_HOSTINFO_KEY);
        log.info("从redis中获取到的数据为：{}", o);
        if (o == null || !(o instanceof List)) {
            list = Lists.newArrayList();
        } else {
            list = (List<String>) o;
        }
        return list;
    }

    @SneakyThrows
    public void updateSso(HostInfo hostInfo, List<String> list) {
        // 执行docker run -d --log-opt max-size=50m --log-opt max-file=3 --network=host
        // --name=sso --privileged=true -v
        // home/sso_upload_file/:/home/<USER>/ --restart=always -it sso:v2.0
        // /bin/bash
        List<String> allDockerContainer = getAllDockerContainer();
        log.info("从docker中获取到的容器为：{}", allDockerContainer);
        if (allDockerContainer.contains("sso")) {
            executeCommand("docker", "rm", "-f", "sso");
        }
        if (CollUtil.isEmpty(list)) {
            list = getHostKeyList(hostInfo);
        }
        if (CollUtil.isEmpty(list)) {
            log.info("没有从redis中获取到主机信息，无法更新sso服务");
            return;
        }
        StringBuilder dbUrl = new StringBuilder();
        dbUrl.append("jdbc:opengauss://");
        StringBuilder redisSentinelNodes = new StringBuilder();
        String normalIp;
        for (String ip : list) {
            // 去掉HOSTINFO::
            normalIp = ip.substring(Constants.REDIS_HOSTINFO_KEY.length() + 2);
            dbUrl.append(normalIp).append(":").append(Constants.GAUSS_LOCALSERVICE).append(",");
            redisSentinelNodes.append(normalIp).append(":26379,");
        }
        // 去掉最后一个逗号
        dbUrl.deleteCharAt(dbUrl.length() - 1);
        redisSentinelNodes.deleteCharAt(redisSentinelNodes.length() - 1);
        dbUrl.append("/postgres?batchMode=off&autoBalance=roundrobin&targetServerType=master");
        String dbPassword = AESUtils.isAESEnCode(hostInfo.getDbPassword())
                ? AESUtils.AESDeCode(hostInfo.getDbPassword())
                : hostInfo.getDbPassword();
        // *************************************,***************:5432/sso?batchMode=off&autoBalance=roundrobin&targetServerType=master
        executeCommand("docker", "run", "-d", "--log-opt", "max-size=50m", "--log-opt", "max-file=3", "--network=host",
                "--name=sso", "--privileged=true", "-v", "/home/<USER>/:/home/<USER>/", "-v",
                "/home/<USER>/icm/upgrade/:/home/<USER>/icm/upgrade/", "-v",
                "/home/<USER>/icm/initclient/:/home/<USER>/icm/initclient/", "--restart=always", "--ulimit",
                "nofile=65536:65536", "sso", "--spring.datasource.url=" + dbUrl,
                "--spring.datasource.password=" + dbPassword, "--spring.redis.sentinel.nodes=" + redisSentinelNodes,"--trust.gateway.host=" +hostInfo.getVipIp());
        Thread.sleep(5000);
        // 添加健康检查 30秒超时
        checkServiceAvailability("sso", 30);
        log.info("完成sso部署");
    }

    @SneakyThrows
    public void deployNginxMaster(HostInfo hostInfo, List<String> list) {
        if (CollUtil.isEmpty(list)) {
            list = getHostKeyList(hostInfo);
        }
        if (CollUtil.isEmpty(list)) {
            log.info("没有从redis中获取到主机信息，无法部署nginx");
            return;
        }

        String configPath = Constants.XC_NGINX_CONF_DIR + "/nginx.conf";
        StringBuilder newUpstreamConfig = new StringBuilder();
        // 组装新的upstream配置
        newUpstreamConfig.append("upstream ssoserver {\n");
        // 连接sentinel，获取主节点信息
        String masterIp = getMasterIpFromSentinelOrLatestLiveIp(hostInfo);
        if (!list.contains("HOSTINFO::" + masterIp)) {
            // 去掉HOSTINFO::
            masterIp = list.get(0);
            masterIp = masterIp.substring(Constants.REDIS_HOSTINFO_KEY.length() + 2);
        }
        String normalIp;
        for (String ip : list) {
            normalIp = ip.substring(Constants.REDIS_HOSTINFO_KEY.length() + 2);
            if (Objects.equals(normalIp, masterIp)) {
                newUpstreamConfig.append("    server ").append(normalIp).append(":9009;")
                        .append(Constants.LINE_SEPARATOR);
            } else {
                newUpstreamConfig.append("    server ").append(normalIp).append(":9009 backup;")
                        .append(Constants.LINE_SEPARATOR);
            }
        }
        newUpstreamConfig.append("    }").append(Constants.LINE_SEPARATOR);
        try {
            updateUpstream(configPath, newUpstreamConfig.toString());
            log.info("Nginx配置更新成功！");
        } catch (IOException e) {
            log.error("配置更新失败: " + e.getMessage());
            e.printStackTrace();
        }
        // 如果容器存在，重启，不存在启动
        List<String> allDockerContainer = getAllDockerContainer();
        if (allDockerContainer.contains("nginxsso")) {
            executeCommand("docker", "restart", "nginxsso");
        } else {
            // docker run --name=nginxsso --privileged=true --restart=always --network=host
            // -v /home/<USER>/nginx/ssl:/etc/nginx/ssl
            // -v /home/<USER>/nginx/nginx.conf:/etc/nginx/nginx.conf
            // -v /home/<USER>/nginx/conf.d:/etc/nginx/conf.d
            // -v /home/<USER>/nginx/logs:/var/log/nginx
            // -d nginx
            executeCommand("docker", "run", "--name=nginxsso", "--privileged=true", "--restart=always",
                    "--network=host", "-v", Constants.BASE_DIR + "/nginx/ssl" + ":/etc/nginx/ssl", "-v",
                    Constants.BASE_DIR + "/nginx/nginx.conf" + ":/etc/nginx/nginx.conf", "-v",
                    Constants.BASE_DIR + "/nginx/conf.d" + ":/etc/nginx/conf.d", "-v",
                    Constants.BASE_DIR + "/nginx/logs" + ":/var/log/nginx", "-d", "nginx");
            // 添加健康检查 30秒超时
            checkServiceAvailability("nginxsso", 30);
        }
        log.info("完成Nginx部署");
    }

    private void updateUpstream(String configPath, String newUpstreamConfig) throws IOException {
        Path path = Paths.get(configPath);
        if (!Files.exists(path)) {
            Files.createDirectories(path.getParent());
        }
        // 备份原文件（可选但推荐）
        if (!Files.exists(path)) {
            // 把conf的nginx.conf复制到path目录
            String configContent = readConfigContent(Constants.NGINX_CONF);
            writeConfiguration(Constants.XC_NGINX_CONF_DIR + "/nginx.conf", configContent);
        }
        Files.copy(path, path.resolveSibling("nginx.conf.bak"), StandardCopyOption.REPLACE_EXISTING);
        // 读取配置文件内容
        String content = new String(Files.readAllBytes(path));
        // 使用正则表达式匹配原upstream块（支持多行和空格）
        String regex = "(?s)upstream ssoserver\\s*\\{.*?\\}";
        String updatedContent = content.replaceAll(regex, newUpstreamConfig);
        // 检查是否实际发生了替换
        if (updatedContent.equals(content)) {
            throw new IOException("未找到upstream ssoserver配置，请检查正则表达式或文件内容");
        }
        // 写回更新后的内容
        Files.write(path, updatedContent.getBytes());
    }

    @SneakyThrows
    public void deployKeepalivedMaster(HostInfo hostInfo) {
        // 主要是更新/etc/keepalived/keepalived.conf文件
        String configContent = readConfigContent(Constants.KEEPALIVED_CONF).replace("${localIp}", hostInfo.getLocalIp())
                .replace("${stateType}", Constants.KEEPALIVED_MASTER)
                .replace("${networkCard}", hostInfo.getNetworkCard())
                .replace("${priorityNum}", Constants.KEEPALIVED_MASTER_PRIORITY_NUM)
                .replace("${vipIp}", hostInfo.getVipIp());

        writeConfiguration(Constants.XC_KEEPALIVED_DIR + "/keepalived.conf", configContent);
        restartKeepalived();
        log.info("完成主服务Keepalived部署");
    }

    private void restartKeepalived() {
        try {
            executeCommand("systemctl", "enable", "keepalived");
            executeCommand("systemctl", "stop", "keepalived");
            executeCommand("systemctl", "start", "keepalived");
        } catch (Exception e) {
            log.error("keepalived服务启动失败", e);
        }
    }

    private boolean isDockerNetworkExists(String networkName) throws IOException, InterruptedException {
        Process process = new ProcessBuilder("docker", "network", "ls", "--format", "{{.Name}}")
                .redirectErrorStream(true).start();
        List<String> networkNames = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (!line.trim().isEmpty()) {
                    networkNames.add(line.trim());
                }
            }
        }
        int exitCode = process.waitFor();
        if (exitCode != 0) {
            throw new IOException("执行docker network命令失败，退出码：" + exitCode);
        }
        return networkNames.contains(networkName);
    }

    @SneakyThrows
    public void deployGaussMaster(HostInfo hostInfo, List<String> list) {
        List<String> dockerContainer = getAllDockerContainer();
        if (dockerContainer.contains("opengauss")) {
            executeCommand("docker", "rm", "-f", "opengauss");
        }

        // 检查网络是否存在，不存在才创建
        // if (!isDockerNetworkExists("opengaussnetwork")) {
        // executeCommand("docker", "network", "create", "--subnet=**********/24",
        // "opengaussnetwork");
        // }
        // 如果redis中节点消息有，修改REPL_CONN_INFO
        String masterIp = getMasterIp(hostInfo);
        if (CollUtil.isEmpty(list)) {
            list = getHostKeyList(hostInfo);
        }

        // size = 1 HOSTINFO::*************:hostInfo
        // 如果redis中节点消息不存在，则获取hostInfo.getLocalIp()，并在其的最后一位+1
        String remotehostIp = hostInfo.getLocalIp().substring(0, hostInfo.getLocalIp().lastIndexOf(".") + 1)
                + (Integer.parseInt(hostInfo.getLocalIp().substring(hostInfo.getLocalIp().lastIndexOf(".") + 1)) + 1);

        String dbPassword = AESUtils.isAESEnCode(hostInfo.getDbPassword())
                ? AESUtils.AESDeCode(hostInfo.getDbPassword())
                : hostInfo.getDbPassword();
        executeCommand("docker", "run", "--network=host", "--restart=always", "--privileged=true", "--name",
                "opengauss", "-h", "opengauss", "-d", "-e", "GS_PORT=5432", "-e", "OG_SUBNET=bridge", "-e",
                "GS_PASSWORD=" + dbPassword, "-e", "NODE_NAME=opengauss", "-e",
                "REPL_CONN_INFO= replconninfo1 = 'localhost=" + hostInfo.getLocalIp()
                        + " localport=5434 localservice=5432 remotehost=" + remotehostIp + " remoteport=5434 "
                        + "remoteservice=5432'  ",
                "-v", Constants.GAUSS_DIR + ":/var/lib/opengauss",
                "-v", Constants.BASE_DIR + "/icm/init:/docker-entrypoint-initdb.d",
                "enmotech/opengauss:5.0.3", "-M", "primary");
        Thread.sleep(10000);
        // 添加健康检查 60秒超时
        // checkServiceAvailability("opengauss", 30);
        checkGaussAvailability(hostInfo.getLocalIp(), dbPassword, 60);
        if (list.size() > 1) {
            dealUpdateOpenGaussConf(hostInfo, masterIp, list);
        }
        log.info("完成主服务Gauss部署");
    }

    private void deploySlaveServices(HostInfo hostInfo) {
        validateSlaveConfiguration(hostInfo);
        try {
            log.info("开始备服务部署：{}", hostInfo);
            createRequiredDirectories();
            log.info("开始备服务器创建相关目录----------");
            deployRedisSlave(hostInfo);
            log.info("开始备服务器部署Redis服务----------");
            deploySentinel(hostInfo);
            log.info("开始备服务器部署Gauss服务----------");
            deployGaussSlave(hostInfo);
            List<String> list = getHostKeyList(hostInfo);
            log.info("开始备服务器部署Keepalived服务----------");
            deployKeepalivedSlave(hostInfo);
            log.info("开始备服务器部署Nginx服务----------");
            deployNginxMaster(hostInfo, list);
            // 更新sso服务,主要是数据库url和redis配置
            log.info("开始备服务器部署sso服务----------");
            updateSso(hostInfo, list);
            // 更新网关的服务
            log.info("开始主服务器部署网关服务----------");
            updateGateway(hostInfo, list);
            // 更新minio 服务
            log.info("开始备服务器部署minio服务----------");
            updateMinioSlave(hostInfo, list);

            redisUtils.publish(hostInfo.getRemoteIp(), Constants.REDIS_UPDATE_NOTICE_KEY,
                    Constants.UPDATE_SSO + ":" + hostInfo.getRemoteIp());
        } catch (Exception e) {
            throw new RuntimeException("备服务部署失败: " + e.getMessage(), e);
        }
    }

    @SneakyThrows
    private void updateMinioSlave(HostInfo hostInfo, List<String> list) {
        // 使用配置文件中的认证信息启动MinIO容器
        List<String> dockerContainer = getAllDockerContainer();
        log.info("存在的容器：{}", dockerContainer);
        if (dockerContainer.contains("minio")) {
            executeCommand("docker", "rm", "-f", "minio");
        }
        executeCommand("docker", "run", "--network=host", "--name", "minio", "-d", "--restart=always",
                "-e", "MINIO_ACCESS_KEY=" + minioAccessKey, "-e", "MINIO_SECRET_KEY=" + minioSecretKey,
                "-v", "/home/<USER>/data:/data", "minio/minio", "server", "/data",
                "--console-address", ":9002", "-address", ":9001");

        // 初始化MinIO bucket（使用配置文件中的认证信息）
        String minioEndpoint = "http://" + hostInfo.getRemoteIp() + ":9001";
        minioInitializer.initializeBucketWithCustomEndpoint(minioEndpoint);

        // 设置主备同步（使用配置文件中的认证信息）
        executeCommand("mc", "alias", "set", "minio_master", "http://" + hostInfo.getRemoteIp() + ":9001",
                minioAccessKey, minioSecretKey);
        executeCommand("mc", "alias", "set", "minio_slave", "http://" + hostInfo.getLocalIp() + ":9001", minioAccessKey,
                minioSecretKey);
        // 使用 --watch 会一直前台运行导致当前线程阻塞，这里放到后台执行避免接口不返回
        executeCommand("sh", "-c",
                "nohup mc mirror --remove --overwrite --watch minio_master minio_slave > /home/<USER>/mirror.log 2>&1 &");
    }

    @SneakyThrows
    public void deployKeepalivedSlave(HostInfo hostInfo) {
        // 主要是更新/etc/keepalived/keepalived.conf文件
        String configContent = readConfigContent(Constants.KEEPALIVED_CONF).replace("${localIp}", hostInfo.getLocalIp())
                .replace("${stateType}", Constants.KEEPALIVED_BACKUP)
                .replace("${networkCard}", hostInfo.getNetworkCard())
                .replace("${priorityNum}", Constants.KEEPALIVED_BACKUP_PRIORITY_NUM)
                .replace("${vipIp}", hostInfo.getVipIp());

        writeConfiguration(Constants.XC_KEEPALIVED_DIR + "/keepalived.conf", configContent);
        restartKeepalived();
        log.info("完成备服务Keepalived部署");
    }

    @SneakyThrows
    public void deployGaussSlave(HostInfo hostInfo) {
        // 从redis中主节点获取数据库密码
        List<String> dockerContainer = getAllDockerContainer();
        if (dockerContainer.contains("opengauss")) {
            executeCommand("docker", "rm", "-f", "opengauss");
        }
        // 删除服务器上的 /home/<USER>/gauss5/data/目录
        executeCommand("rm", "-rf", Constants.GAUSS_DATA_DIR);
        // 检查网络是否存在，不存在才创建
        // if (!isDockerNetworkExists("opengaussnetwork")) {
        // executeCommand("docker", "network", "create", "--subnet=**********/24",
        // "opengaussnetwork");
        // }
        // 通知主机修改的/home/<USER>/gauss5/data/postgresql.conf 的REPL_CONN_INFO
        redisUtils.publish(hostInfo.getRemoteIp(), Constants.REDIS_UPDATE_NOTICE_KEY,
                Constants.MODIFY_REPL_CONN_INFO + ":" + hostInfo.getRemoteIp());
        Thread.sleep(10000);

        String dbPassword = AESUtils.isAESEnCode(hostInfo.getDbPassword())
                ? AESUtils.AESDeCode(hostInfo.getDbPassword())
                : hostInfo.getDbPassword();
        // 检查当前副表数量，增加和删除REPL_CONN_INFO todo
        executeCommand("docker", "run", "--network=host", "--restart=always", "--privileged=true", "--name",
                "opengauss", "-h", "opengauss", "-d", "-e", "GS_PORT=5432", "-e", "OG_SUBNET=bridge", "-e",
                "GS_PASSWORD=" + dbPassword, "-e", "NODE_NAME=opengauss", "-e",
                "REPL_CONN_INFO=replconninfo1 = 'localhost=" + hostInfo.getLocalIp()
                        + " localport=5434 localservice=5432 remotehost=" + hostInfo.getRemoteIp() + " remoteport=5434 "
                        + "remoteservice=5432' ",
                "-v", Constants.GAUSS_DIR + ":/var/lib/opengauss",
                "-v", Constants.BASE_DIR + "/icm/init:/docker-entrypoint-initdb.d",
                "enmotech/opengauss:5.0.3", "-M", "standby");
        // 添加健康检查 30秒超时
        // checkServiceAvailability("opengauss", 30);
        Thread.sleep(10000);
        checkGaussAvailability(hostInfo.getLocalIp(), dbPassword, 30);
        log.info("完成备服务Gauss部署");
    }

    private void validateSlaveConfiguration(HostInfo hostInfo) {
        if (StrUtil.isBlank(hostInfo.getRemoteIp())) {
            throw new BusinessException("备机配置需要指定主机的IP地址");
        }
    }

    public void addMasterInfo(HostInfo hostInfo) {
        String masterIp = getMasterIp(hostInfo);
        Object object = redisUtils.redisGet(masterIp, Constants.REDIS_HOSTINFO_KEY + "::" + masterIp);
        // 将object转为map
        if (object == null) {
            throw new BusinessException("获取主节点信息失败");
        }
        HostInfo masterInfo = JSONUtil.toBean(object.toString(), HostInfo.class);
        hostInfo.setDbPassword(masterInfo.getDbPassword());
        hostInfo.setVipIp(masterInfo.getVipIp());
    }

    private void createRequiredDirectories() throws IOException, InterruptedException {
        executeCommand("mkdir", "-p", Constants.REDIS_CONF_DIR, Constants.REDIS_DATA_DIR, Constants.SENTINEL_DIR,
                Constants.GAUSS_DIR, Constants.GAUSS_INIT_DIR);

        // 复制初始化脚本到指定目录
        copyInitScripts();

        log.info("完成相关目录创建和初始化脚本复制");
    }

    /**
     * 复制初始化脚本到Docker挂载目录
     */
    private void copyInitScripts() throws IOException, InterruptedException {
        // 检查类路径中是否存在初始化脚本
        InputStream inputStream = getClass().getResourceAsStream("/docker-init/init-user.sql");
        if (inputStream != null) {
            try {
                // 使用Java标准方法复制文件
                Path targetPath = Paths.get(Constants.GAUSS_INIT_DIR, "init-user.sql");
                Files.createDirectories(targetPath.getParent());
                Files.copy(inputStream, targetPath, StandardCopyOption.REPLACE_EXISTING);
                log.info("成功复制初始化脚本到: {}", targetPath);
            } finally {
                inputStream.close();
            }
        } else {
            log.warn("未找到初始化脚本: /docker-init/init-user.sql");
        }
    }

    private void deployRedisMaster(HostInfo hostInfo) throws IOException, InterruptedException {
        String configContent = readConfigContent(Constants.REDIS_MASTER_CONF).replace("${masterAuth}",
                Constants.DEFAULT_PASSWORD);
        writeConfiguration(Constants.REDIS_CONF_DIR + "/redis.conf", configContent);
        startRedisContainer();
        String masterIp = getMasterIp(hostInfo);
        redisUtils.redisSet(masterIp, Constants.REDIS_MASTER_IP_KEY, masterIp);
        redisUtils.redisSet(masterIp, Constants.REDIS_HOSTINFO_KEY + "::" + masterIp, JSONUtil.toJsonStr(hostInfo));
        log.info("完成主服务Redis部署");
    }

    private void deployRedisSlave(HostInfo hostInfo) throws IOException, InterruptedException {
        String masterIp = getMasterIp(hostInfo);
        String configContent = readConfigContent(Constants.REDIS_SLAVE_CONF).replace("${masterIp}", masterIp)
                .replace("${masterAuth}", Constants.DEFAULT_PASSWORD).replace("${masterPort}", Constants.REDIS_PORT);
        writeConfiguration(Constants.REDIS_CONF_DIR + "/redis.conf", configContent);
        startRedisContainer();
        log.info("完成备服务Redis部署");
        redisUtils.redisSet(masterIp, Constants.REDIS_HOSTINFO_KEY + "::" + hostInfo.getLocalIp(),
                JSONUtil.toJsonStr(hostInfo));
    }

    private void deploySentinel(HostInfo hostInfo) throws IOException, InterruptedException {
        String masterIp = getMasterIp(hostInfo);
        // Object o = redisUtils.redisGetKey(hostInfo.getLocalIp(),
        // Constants.REDIS_HOSTINFO_KEY);
        // size = 1 HOSTINFO::*************
        // 将o转为list
        // List<String> list = (List<String>)o;
        int sentinelNum = 1;
        // if (o != null && list.size() > 2) {
        // sentinelNum = list.size() / 2 + 1;
        // }
        String configContent = readConfigContent(Constants.SENTINEL_CONF).replace("${masterIp}", masterIp)
                .replace("${masterPort}", Constants.REDIS_PORT).replace("${masterAuth}", Constants.DEFAULT_PASSWORD)
                .replace("${sentinelNum}", sentinelNum + "");
        writeConfiguration(Constants.SENTINEL_DIR + "/sentinel.conf", configContent);
        // 如果是备机修改remoteIp机器的sentinel.conf文件中的sentinelNum，并通知其他机器更新
        if (Constants.HOST_TYPE_SLAVE.equals(hostInfo.getHostType())) {
            // 往redis发送消息，通知其他机器更新sentinel.conf文件
            redisUtils.publish(masterIp, Constants.REDIS_UPDATE_NOTICE_KEY, "update:" + hostInfo.getLocalIp());
        }
        startSentinelContainer();
        log.info("完成Sentinel部署");
    }

    public static String getMasterIp(HostInfo hostInfo) {
        String masterIp;
        if (Constants.HOST_TYPE_MASTER.equals(hostInfo.getHostType())) {
            masterIp = hostInfo.getLocalIp();
        } else {
            masterIp = hostInfo.getRemoteIp();
        }
        return masterIp;
    }

    /**
     * 获取当前主节点的IP地址
     *
     * @return 主节点的IP地址
     */
    private String getMasterIpFromSentinel() {
        String sentinelIp = getHostConfig(null).getLocalIp();
        int sentinelPort = 26379;

        try {
            RedisTemplate<String, Object> template = redisUtils.waitForRedisConnection(sentinelIp, sentinelPort,
                    Constants.DEFAULT_PASSWORD);
            if (template != null) {
                // 获取Sentinel连接
                RedisConnectionFactory connectionFactory = template.getConnectionFactory();
                if (connectionFactory != null) {
                    RedisSentinelConnection sentinelConnection = connectionFactory.getSentinelConnection();
                    // 使用正确的方法获取主节点地址
                    String masterName = "mymaster"; // 确保与Sentinel配置中的master名称一致
                    Collection<RedisServer> masterInfo = sentinelConnection.masters();
                    if (CollUtil.isNotEmpty(masterInfo)) {
                        for (RedisServer redisServer : masterInfo) {
                            if (masterName.equalsIgnoreCase(redisServer.getName())) {
                                return redisServer.getHost();
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("从 Sentinel 获取主节点 IP 时出错", e);
        }
        return null;
    }

    public String getMasterIpFromSentinelOrLatestLiveIp(HostInfo hostConfig) {
        String sentinelIp = hostConfig.getLocalIp();
        if (StrUtil.isBlank(sentinelIp)) {
            log.error("Sentinel IP 为空");
            return null;
        }
        int sentinelPort = 26379;
        String masterIp = null;
        boolean isOnline = false;
        RedisTemplate<String, Object> template = null;
        try {
            template = redisUtils.waitForRedisConnection(sentinelIp, sentinelPort, Constants.DEFAULT_PASSWORD);
            if (template != null) {
                // 获取Sentinel连接
                RedisConnectionFactory connectionFactory = template.getConnectionFactory();
                if (connectionFactory != null) {
                    RedisSentinelConnection sentinelConnection = connectionFactory.getSentinelConnection();
                    // 使用正确的方法获取主节点地址
                    String masterName = "mymaster"; // 确保与Sentinel配置中的master名称一致
                    Collection<RedisServer> masterInfo = sentinelConnection.masters();
                    if (CollUtil.isNotEmpty(masterInfo)) {
                        for (RedisServer redisServer : masterInfo) {
                            if (masterName.equalsIgnoreCase(redisServer.getName())) {
                                masterIp = redisServer.getHost();
                                // 测试连通性，每2秒检测一次，一共3次
                                int retryCount = 3;
                                int retryInterval = 2;
                                for (int i = 0; i < retryCount; i++) {
                                    if (NetworkUtils.isPortOpen(masterIp, Integer.parseInt(Constants.REDIS_PORT),
                                            1000)) {
                                        log.info("主机 {} 已连通", masterIp);
                                        isOnline = true;
                                        break;
                                    }
                                    Thread.sleep(retryInterval * 1000);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("从Sentinel获取主IP时出错", e);
        }

        if (isOnline) {
            return masterIp;
        } else {
            // Object o = redisUtils.redisGetKey(template, Constants.REDIS_HOSTINFO_KEY);
            log.info("-------------------开始从redis中获取其他机器，作为主机");
            List<String> list = getHostKeyList(hostConfig);
            if (CollUtil.isEmpty(list)) {
                log.error("从redis中获取其他机器数据无效");
                return null;
            }
            // 并行检查每个主机的连通性，取第一个连通的主机
            List<CompletableFuture<String>> futures = list.stream().map(s -> CompletableFuture.supplyAsync(() -> {
                try {
                    String ip = s.substring(Constants.REDIS_HOSTINFO_KEY.length() + 2);
                    if (StrUtil.isBlank(ip)) {
                        log.error("从 Redis 中检索到的数据{}无效，提取IP为空", s);
                        return null;
                    }
                    if (NetworkUtils.isPortOpen(ip, Integer.parseInt(Constants.REDIS_PORT), 1000)) {
                        return ip;
                    } else {
                        log.warn("从 Redis 中检索到的数据，其中IP{}无法连通，跳过", ip); // 调整为warn级别
                        return null;
                    }
                } catch (NumberFormatException e) {
                    log.error("端口号格式异常: {}", Constants.REDIS_PORT, e);
                    return null;
                } catch (Exception e) {
                    log.error("连通性检查异常", e);
                    return null;
                }
            }, CHECK_POOL)).filter(Objects::nonNull).collect(Collectors.toList());

            // 使用anyOf等待首个有效响应
            try {
                CompletableFuture<Object> firstCompleted = CompletableFuture
                        .anyOf(futures.toArray(new CompletableFuture[0]));

                String validIp = (String) firstCompleted.get(2, TimeUnit.SECONDS); // 设置总超时时间
                if (validIp != null) {
                    log.info("从redis中获取其他机器，选取IP为主机：{}", validIp);
                    // 通知sentinel切换到新主机
                    RedisConnectionFactory connectionFactory = template.getConnectionFactory();
                    if (connectionFactory != null) {
                        try (RedisSentinelConnection sentinelConnection = connectionFactory.getSentinelConnection()) {
                            // 修正后的完整实现
                            String masterName = "mymaster"; // 确保与Sentinel配置一致
                            RedisServer newMaster = new RedisServer(validIp, Integer.parseInt(Constants.REDIS_PORT));
                            newMaster.setName(masterName);

                            log.info("正在触发哨兵故障转移到新主机：{}:{}", validIp, Constants.REDIS_PORT);
                            try {
                                sentinelConnection.failover(newMaster);
                                log.info("故障转移指令已成功发送至哨兵");

                                // 添加转移状态确认
                                if (isFailoverComplete(sentinelConnection, newMaster)) {
                                    log.info("故障转移已成功完成");
                                } else {
                                    log.warn("故障转移状态未达预期，建议人工介入检查");
                                }
                            } catch (Exception ex) {
                                String err = ex.getMessage();
                                log.warn("哨兵故障转移失败：{}，尝试执行强制切换...", err);
                                if (err != null && err.contains("NOGOODSLAVE")) {
                                    forcePromoteToMaster(validIp, hostConfig);
                                } else {
                                    throw ex;
                                }
                            }
                        } catch (IllegalArgumentException e) {
                            log.error("无效的主节点参数：{}", e.getMessage());
                            throw new BusinessException("非法的故障转移目标节点");
                        } catch (RedisConnectionFailureException e) {
                            log.error("哨兵连接异常，故障转移失败", e);
                            throw new BusinessException("无法连接哨兵服务");
                        }
                    }
                    return validIp;
                }
            } catch (TimeoutException e) {
                log.warn("所有主机连通性检查超时");
            } catch (Exception e) {
                log.error("主机选择异常", e);
            } finally {
                // 取消所有未完成任务
                futures.forEach(f -> f.cancel(true));
            }
        }
        return null;
    }

    // 新增状态检查方法
    private boolean isFailoverComplete(RedisSentinelConnection connection, RedisServer expectedMaster) {
        try {
            return connection.masters().stream().filter(m -> m.getName().equals(expectedMaster.getName()))
                    .anyMatch(m -> m.getHost().equals(expectedMaster.getHost()));
        } catch (RedisConnectionException e) {
            log.warn("故障转移状态检查失败", e);
            return false;
        }
    }

    /**
     * 当 Sentinel failover 返回 NOGOODSLAVE 时的兜底：
     * 1) 将目标节点强制提升为主库（REPLICAOF NO ONE）
     * 2) 让其它节点全部跟随它（REPLICAOF newMaster）
     * 3) 重配并重启 Sentinel 指向新主
     */
    private void forcePromoteToMaster(String newMasterIp, HostInfo hostConfig) {
        log.warn("开始执行强制切换，目标新主：{}", newMasterIp);
        try {
            // 1) 提升候选为主
            RedisTemplate<String, Object> promoteTpl = redisUtils.waitForRedisConnection(newMasterIp,
                    Integer.parseInt(Constants.REDIS_PORT), Constants.DEFAULT_PASSWORD);
            if (promoteTpl == null) {
                throw new BusinessException("无法连接到候选新主节点：" + newMasterIp);
            }
            RedisConnection promoteConn = promoteTpl.getConnectionFactory().getConnection();
            try {
                doReplicaOfNoOne(promoteConn);
                log.info("已将 {} 提升为主库", newMasterIp);
            } finally {
                try {
                    promoteConn.close();
                } catch (Exception ignore) {
                }
            }

            // 2) 其它机器改为从新主
            List<String> keyList = getHostKeyList(hostConfig);
            if (CollUtil.isNotEmpty(keyList)) {
                for (String key : keyList) {
                    String ip = null;
                    try {
                        ip = key.substring(Constants.REDIS_HOSTINFO_KEY.length() + 2);
                    } catch (Exception ignore) {
                    }
                    if (StrUtil.isBlank(ip) || Objects.equals(ip, newMasterIp)) {
                        continue;
                    }
                    try {
                        RedisTemplate<String, Object> tpl = redisUtils.waitForRedisConnection(ip,
                                Integer.parseInt(Constants.REDIS_PORT), Constants.DEFAULT_PASSWORD);
                        if (tpl == null) {
                            log.warn("无法连接到节点 {}，跳过从库重配", ip);
                            continue;
                        }
                        RedisConnection conn = tpl.getConnectionFactory().getConnection();
                        try {
                            doReplicaOf(conn, newMasterIp, Integer.parseInt(Constants.REDIS_PORT));
                            log.info("已将 {} 配置为跟随新主 {}", ip, newMasterIp);
                        } finally {
                            try {
                                conn.close();
                            } catch (Exception ignore) {
                            }
                        }
                    } catch (Exception ex) {
                        log.warn("重配从库 {} 失败: {}", ip, ex.getMessage());
                    }
                }
            }

            // 3) 让哨兵指向新主：这里复用现有部署流程，避免直接操作 SENTINEL 命令
            try {
                updateSentinelMasterTo(newMasterIp);
            } catch (Exception se) {
                log.warn("更新哨兵指向新主失败: {}，请检查 sentinel 配置", se.getMessage());
            }
            log.warn("强制切换已完成，当前新主：{}", newMasterIp);
        } catch (BusinessException be) {
            throw be;
        } catch (Exception e) {
            log.error("强制切换过程中出现异常", e);
            throw new BusinessException("强制切换失败：" + e.getMessage());
        }
    }

    /**
     * 将 sentinel 监控的主机切换为指定 IP。
     * 最小改动：沿用已有的 startSentinelContainer() 和 resetSubscription()。
     */
    private void updateSentinelMasterTo(String newMasterIp) throws IOException, InterruptedException {
        // 直接用已有逻辑：读取模板并重启容器。为了最小改动，这里不重写文件，只重启，
        // 若你的 sentinel.conf 渲染依赖 getMasterIpFromSentinelOrLatestLiveIp，请按需调整。
        // 更保险的做法是扩展一个按 IP 渲染写盘的方法，这里保持轻量级。
        startSentinelContainer();
        redisMessageListener.resetSubscription();
        log.info("已请求 Sentinel 重启以感知新主：{}", newMasterIp);
    }

    // 兼容不同驱动方法命名：优先调用 replicaOfNoOne/replicaOf，不存在则退回到底层命令
    private void doReplicaOfNoOne(RedisConnection connection) {
        try {
            connection.getClass().getMethod("replicaOfNoOne").invoke(connection);
            return;
        } catch (Exception ignore) {
        }
        try {
            connection.getClass().getMethod("slaveOfNoOne").invoke(connection);
            return;
        } catch (Exception ignore) {
        }
        try {
            connection.execute("REPLICAOF", "NO".getBytes(StandardCharsets.UTF_8),
                    "ONE".getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("执行 REPLICAOF NO ONE 失败: {}", e.getMessage());
            throw new BusinessException("执行 REPLICAOF NO ONE 失败");
        }
    }

    private void doReplicaOf(RedisConnection connection, String host, int port) {
        try {
            connection.getClass().getMethod("replicaOf", String.class, int.class).invoke(connection, host, port);
            return;
        } catch (Exception ignore) {
        }
        try {
            connection.getClass().getMethod("slaveOf", String.class, int.class).invoke(connection, host, port);
            return;
        } catch (Exception ignore) {
        }
        try {
            connection.execute("REPLICAOF", host.getBytes(StandardCharsets.UTF_8),
                    String.valueOf(port).getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("执行 REPLICAOF {}:{} 失败: {}", host, port, e.getMessage());
            throw new BusinessException("执行 REPLICAOF 失败");
        }
    }

    private String getNowActiveIpFromSentinel() {
        String sentinelIp = getHostConfig(null).getLocalIp();
        int sentinelPort = 26379;

        try {
            RedisTemplate<String, Object> template = redisUtils.waitForRedisConnection(sentinelIp, sentinelPort,
                    Constants.DEFAULT_PASSWORD);
            if (template != null) {
                // 获取Sentinel连接
                RedisConnectionFactory connectionFactory = template.getConnectionFactory();
                if (connectionFactory != null) {
                    RedisSentinelConnection sentinelConnection = connectionFactory.getSentinelConnection();
                    // 使用正确的方法获取主节点地址
                    String masterName = "mymaster"; // 确保与Sentinel配置中的master名称一致
                    Collection<RedisServer> masterInfo = sentinelConnection.masters();
                    if (CollUtil.isNotEmpty(masterInfo)) {
                        for (RedisServer redisServer : masterInfo) {
                            if (masterName.equalsIgnoreCase(redisServer.getName())) {
                                return redisServer.getHost();
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("从 Sentinel 获取主节点 IP 时出错", e);
        }
        return null;
    }

    private String readConfigContent(String resourcePath) throws IOException {
        try (InputStream is = getClass().getResourceAsStream(resourcePath)) {
            if (is == null)
                throw new FileNotFoundException("未找到配置文件: " + resourcePath);

            ByteArrayOutputStream result = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int length;
            while ((length = is.read(buffer)) != -1) {
                result.write(buffer, 0, length);
            }
            return result.toString(StandardCharsets.UTF_8.name());
        }
    }

    private String readLinuxConfigContent(String resourcePath) throws IOException {
        try (InputStream is = Files.newInputStream(Paths.get(resourcePath))) {
            if (is == null)
                throw new FileNotFoundException("未找到配置文件: " + resourcePath);

            ByteArrayOutputStream result = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int length;
            while ((length = is.read(buffer)) != -1) {
                result.write(buffer, 0, length);
            }
            return result.toString(StandardCharsets.UTF_8.name());
        }
    }

    private void writeConfiguration(String path, String content) throws IOException {
        if (!Files.exists(Paths.get(path))) {
            Files.createDirectories(Paths.get(path).getParent());
        }
        Files.write(Paths.get(path), content.getBytes(), StandardOpenOption.CREATE,
                StandardOpenOption.TRUNCATE_EXISTING);
    }

    private void startRedisContainer() throws IOException, InterruptedException {
        List<String> dockerContainer = getAllDockerContainer();
        if (dockerContainer.contains("redis")) {
            executeCommand("docker", "rm", "-f", "redis");
        }

        executeCommand("docker", "run", "-d", "--privileged=true", "--restart", "always", "-v",
                Constants.REDIS_DATA_DIR + ":/data", "-v",
                Constants.REDIS_CONF_DIR + "/redis.conf:/etc/redis/redis.conf",
                "--name", "redis", "--network=host", Constants.DOCKER_REDIS_IMAGE, "/etc/redis/redis.conf");
        // 添加健康检查 30秒超时
        checkServiceAvailability("redis", 30);
    }

    private void startSentinelContainer() throws IOException, InterruptedException {
        List<String> dockerContainer = getAllDockerContainer();
        log.info("存在的容器：{}", dockerContainer);
        if (dockerContainer.contains("sentinel")) {
            executeCommand("docker", "rm", "-f", "sentinel");
        }
        executeCommand("docker", "run", "-d", "--privileged=true", "--restart", "always", "-v",
                Constants.SENTINEL_DIR + "/sentinel.conf:/etc/redis/sentinel.conf", "--name", "sentinel",
                "--network=host",
                Constants.DOCKER_REDIS_IMAGE, "redis-sentinel", "/etc/redis/sentinel.conf");
        // 添加健康检查 30秒超时
        checkServiceAvailability("sentinel", 30);
    }

    private List<String> getAllDockerContainer() throws IOException, InterruptedException {
        Process process = new ProcessBuilder("docker", "ps", "-a", "--format", "{{.Names}}").redirectErrorStream(true) // 合并标准错误流到输入流
                .start();

        List<String> containerNames = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (!line.trim().isEmpty()) {
                    containerNames.add(line.trim());
                }
            }
        }

        int exitCode = process.waitFor();
        if (exitCode != 0) {
            throw new IOException("执行docker命令失败，退出码：" + exitCode);
        }

        return containerNames;
    }

    private void executeCommand(String... command) throws IOException, InterruptedException {
        String fullCommand = String.join(" ", command);
        log.info("完整命令：{}", fullCommand);
        Process process = new ProcessBuilder(command).start();
        consumeOutputStream(process);
        int exitCode = process.waitFor();
        if (exitCode != 0) {
            throw new BusinessException("命令执行失败: " + String.join(" ", command));
        }
    }

    private void consumeOutputStream(Process process) throws IOException {
        // 创建有名称的守护线程
        Thread outputThread = new Thread(() -> {
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    log.info("[Process Output] {}", line);
                }
            } catch (IOException e) {
                log.error("读取进程流时出错", e);
            }
        }, "Process-Output-Consumer");

        // 单独处理错误流
        Thread errorThread = new Thread(() -> {
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(process.getErrorStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    log.error("[Process Error] {}", line);
                }
            } catch (IOException e) {
                log.error("读取进程错误流时出错", e);
            }
        }, "Process-Error-Consumer");

        outputThread.setDaemon(true);
        errorThread.setDaemon(true);
        outputThread.start();
        errorThread.start();
    }

    /**
     * 更新主机配置
     *
     * @param hostInfo 主机配置
     * <AUTHOR>
     */
    public void updateHostConfig(HostInfo hostInfo) {
        // 判断是否是跳过数据库密码，如果跳过根据当前的IP+MAC地址自动生成一个复杂的密码，包括大写字母，小写字母、特殊字符、数字，密码长度 8~32
        if (StrUtil.isBlank(hostInfo.getDbPassword())) {
            // 获取本机IP和MAC地址作为种子
            String localIp = hostInfo.getLocalIp();
            String macAddress = getMacAddress(localIp);
            // 使用IP和MAC生成密码
            String seed = localIp + macAddress;
            String dbPassword = generateComplexPassword(seed);
            hostInfo.setDbPassword(AESUtils.AESEnCode(dbPassword));
        }

        if (Constants.HOST_TYPE_MASTER.equals(hostInfo.getHostType())) {
            // 校验密码：1 个大写字母、1 个小写字母、1 个数字、1 个特殊字符，至少包含上述四类字符中的三类，密码长度 8~32
            // 字符，不能和用户名、用户名倒写相同，本要求为非大小写敏感，不能和当前密码、当前密码的倒写相同
            boolean validatePassword = PasswordUtils.validatePassword(hostInfo.getDbPassword(),
                    Constants.GAUSS_USERNAME, "");
            if (!validatePassword) {
                throw new BusinessException("数据库密码不符合要求");
            }
        }

        if (StrUtil.isBlank(hostInfo.getId())) {
            hostInfo.setId(IdUtil.getSnowflakeNextIdStr());
            addHostConfig(hostInfo);
            return;
        }
        if (StrUtil.isNotBlank(hostInfo.getPassword())) {
            hostInfo.setPassword(AESUtils.isAESEnCode(hostInfo.getPassword()) ? hostInfo.getPassword()
                    : AESUtils.AESEnCode(hostInfo.getPassword()));
        }
        if (StrUtil.isNotBlank(hostInfo.getDbPassword())) {
            hostInfo.setDbPassword(AESUtils.isAESEnCode(hostInfo.getDbPassword()) ? hostInfo.getDbPassword()
                    : AESUtils.AESEnCode(hostInfo.getDbPassword()));
        }
        // 获取原有配置
        HostInfo oldConfig = getHostConfig(null);
        if (oldConfig != null && StrUtil.isNotBlank(oldConfig.getHostType())
                && !oldConfig.getHostType().equals(hostInfo.getHostType())) {
            throw new BusinessException("主机类型不能修改");
        }
        if (Constants.HOST_TYPE_SLAVE.equals(hostInfo.getHostType())) {
            addMasterInfo(hostInfo);
        }
        // hostInfo转化为Map
        Map<String, Object> map = BeanUtil.beanToMap(hostInfo);
        log.info("更新配置：{}", map);
        jsonDbsonManager.updateRow(hostInfo.getId(), map);

        if (Constants.HOST_TYPE_MASTER.equals(hostInfo.getHostType())) {
            deployMasterServices(hostInfo);
        } else if (Constants.HOST_TYPE_SLAVE.equals(hostInfo.getHostType())) {
            deploySlaveServices(hostInfo);
        }
        // 检查是否需要重置订阅
        boolean needResetSubscription = !hostInfo.getHostType().equals(oldConfig.getHostType());
        // 异步等待 Redis 启动并写入配置
        addInfoToRedis(hostInfo, needResetSubscription);
    }

    private static String getMacAddress(String localIp) {
        String macAddress = "";
        try {
            InetAddress ip = InetAddress.getByName(localIp);
            NetworkInterface network = NetworkInterface.getByInetAddress(ip);
            if (network == null) {
                log.warn("未找到IP对应的网络接口: {}", localIp);
                return "DEFAULT-MAC";
            }

            byte[] mac = network.getHardwareAddress();
            if (mac == null) {
                log.warn("无法获取网络接口的MAC地址: {}", network.getDisplayName());
                return "DEFAULT-MAC";
            }

            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < mac.length; i++) {
                sb.append(String.format("%02X%s", mac[i], (i < mac.length - 1) ? "-" : ""));
            }
            macAddress = sb.toString();
        } catch (Exception e) {
            log.error("获取MAC地址失败: {}", e.getMessage());
            macAddress = "DEFAULT-MAC";
        }
        return macAddress;
    }

    /**
     * 获取主机配置
     *
     * @return 主机配置
     * <AUTHOR>
     */
    @SneakyThrows
    public HostInfo getHostConfig(String localIp) {
        List<Map<String, Object>> data = Optional.ofNullable(jsonDbsonManager.readData())
                .orElseGet(Lists::newArrayList);
        HostInfo hostInfo;
        if (data.isEmpty()) {
            hostInfo = new HostInfo();
            hostInfo.setLocalIp(getLocalIp(localIp));
            hostInfo.setNetworkCard(NetworkUtils.getNetworkInterfaceNameByIP(localIp));
            return hostInfo;
        }
        // Map转化为HostInfo
        hostInfo = BeanUtil.toBean(data.get(0), HostInfo.class);

        // 获取本机Ip
        if (hostInfo.getLocalIp() == null || hostInfo.getLocalIp().isEmpty()) {
            String localIP = getLocalIp(localIp);
            hostInfo.setLocalIp(localIP);
        }
        return hostInfo;
    }

    /**
     * 获取本机Ip
     *
     * @return 本机Ip
     * @throws SocketException 异常
     * <AUTHOR>
     */
    @SneakyThrows
    private String getLocalIp(String localIp) {
        if (StrUtil.isNotBlank(localIp)) {
            return localIp;
        }
        Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
        while (interfaces.hasMoreElements()) {
            NetworkInterface networkInterface = interfaces.nextElement();
            if (networkInterface.isLoopback() || !networkInterface.isUp())
                continue;
            Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
            while (addresses.hasMoreElements()) {
                InetAddress address = addresses.nextElement();
                if (address instanceof Inet4Address && address.isSiteLocalAddress()) {
                    return address.getHostAddress();
                }
            }
        }
        throw new BusinessException("未找到有效的本地 IPv4 地址");
    }

    /**
     * 当收到更新通知时，重新部署
     */
    public void updateConfig(String message) {
        try {
            HostInfo hostInfo = getHostConfig(null);
            if (hostInfo == null) {
                return;
            }
            if (message.contains(Constants.MODIFY_UPDATE)) {
                // 更新Sentinel配置
                updateRedisAndSentinel(hostInfo, message);
            }
            if (message.contains(Constants.MODIFY_REPL_CONN_INFO)) {
                // 更新opengauss配置
                updateOpenGauss(hostInfo, message);
            }
            if (message.contains(Constants.UPDATE_SSO)) {
                // 更新sso 和 gateway
                updateSso(hostInfo, new ArrayList<>());
                updateGateway(hostInfo, new ArrayList<>());
            }
            if (message.contains(Constants.UPDATE_GAUSS_PASSWORD)) {
                String jsonStr = message.substring(message.indexOf(':') + 1).replace("\\\"", "\"");
                UpdateGaussPasswordRequest request = JSONUtil.toBean(jsonStr, UpdateGaussPasswordRequest.class);
                log.info("收到opengauss密码更新通知，更新密码：{}", request);
                dealUpdateGaussAndSso(hostInfo, request.getDbPassword());
            }
            if (message.contains(Constants.UPDATE_VIP)) {
                String jsonStr = message.substring(message.indexOf(':') + 1).replace("\\\"", "\"");
                UpdateVipRequest request = JSONUtil.toBean(jsonStr, UpdateVipRequest.class);
                log.info("收到vip更新通知，更新vip：{}", request);
                dealUpdateVip(hostInfo, request.getVipIp());
            }
        } catch (Exception e) {
            log.error("更新 Sentinel 配置失败", e);
            throw new RuntimeException("更新 Sentinel 配置失败: " + e.getMessage(), e);
        }
    }

    private void dealUpdateVip(HostInfo hostConfig, String vipIp) {
        if (StrUtil.isBlank(vipIp)) {
            log.error("vipIp为空，不更新");
            return;
        }
        log.info("更新vip：{}");
        // hostInfo转化为Map
        hostConfig.setVipIp(vipIp);
        Map<String, Object> map = BeanUtil.beanToMap(hostConfig);
        jsonDbsonManager.updateRow(hostConfig.getId(), map);
        addInfoToRedis(hostConfig, false);
        if (Constants.HOST_TYPE_MASTER.equals(hostConfig.getHostType())) {
            deployKeepalivedMaster(hostConfig);
        } else {
            deployKeepalivedSlave(hostConfig);
        }
        log.info("更新vip成功");
    }

    public static void main(String[] args) {
        String message = Constants.MODIFY_REPL_CONN_INFO + ":" + "***************";
        System.out.println(message.substring(Constants.MODIFY_REPL_CONN_INFO.length() + 1));
        String message2 = "HOSTINFO::*************";
        System.out.println(message2.substring(Constants.REDIS_HOSTINFO_KEY.length() + 2));

        // 测试密码生成
        String macAddress = getMacAddress("************");
        String testSeed = "************" + macAddress;
        System.out.println("测试种子：" + testSeed);

        String generatedPassword = generateComplexPassword(testSeed);
        System.out.println("生成的密码：" + generatedPassword);

        // 验证密码是否符合要求
        boolean isValid = PasswordUtils.validatePassword(generatedPassword, Constants.GAUSS_USERNAME, "");
        System.out.println("密码验证结果：" + (isValid ? "通过" : "不通过"));

        // 测试多个不同的种子
        String[] testSeeds = {
                "192.168.1.100aa:bb:cc:dd:ee:ff",
                "10.0.0.1ff:ee:dd:cc:bb:aa",
                "172.16.0.50ab:cd:ef:12:34:56"
        };

        for (String seed : testSeeds) {
            String pwd = generateComplexPassword(seed);
            boolean valid = PasswordUtils.validatePassword(pwd, Constants.GAUSS_USERNAME, "");
            System.out.println("种子: " + seed + " -> 密码: " + pwd + " -> 验证: " + (valid ? "通过" : "不通过"));
        }
    }

    @SneakyThrows
    private void updateOpenGauss(HostInfo hostInfo, String message) {
        String masterIp = getMasterIpFromSentinelOrLatestLiveIp(hostInfo);
        // 截取："MODIFY_REPL_CONN_INFO:***************" 出 ***************
        if (!message.contains(hostInfo.getLocalIp())) {
            log.info("不是对应主机，不更新，masterIp: {}，localIp：{},message:{}", masterIp, hostInfo.getLocalIp(), message);
            return;
        }
        dealUpdateOpenGaussConf(hostInfo, masterIp, new ArrayList<>());
    }

    private void dealUpdateOpenGaussConf(HostInfo hostInfo, String masterIp, List<String> list)
            throws IOException, InterruptedException {
        /*
         * 修改的/home/<USER>/gauss5/data/postgresql.conf 的REPL_CONN_INFO
         * replconninfo1 = 'localhost=*************** localport=5434 localservice=5432
         * remotehost=*************** remoteport=5434 remoteservice=5432'
         * 增加一行
         * 从redis中获取消息，补充到配置文件中
         */
        if (CollUtil.isEmpty(list)) {
            list = getHostKeyList(hostInfo);
        }
        if (CollUtil.isEmpty(list)) {
            log.error("从 Redis 中检索到的键 {}  的数据无效", Constants.REDIS_HOSTINFO_KEY);
            return;
        }
        HostInfo hostInfoTmp;
        List<String> replConnInfoList = new ArrayList<>();
        HostInfo hostInfoLocal = getHostConfig(null);
        int i = 1;
        for (String key : list) { // 目前key为IP
            if (StrUtil.isBlank(key)) {
                continue;
            }
            hostInfoTmp = JSONUtil.toBean(StringUtils.getValue(redisUtils.redisGet(masterIp, key)), HostInfo.class);
            if (hostInfoLocal.getLocalIp().equals(hostInfoTmp.getLocalIp())) {
                log.info("本机的opengauss，不用添加到MODIFY_REPL_CONN_INFO，masterIp: {}，localIp：{}", masterIp,
                        hostInfo.getLocalIp());
                continue;
            }
            replConnInfoList.add("replconninfo" + i + " = 'localhost=" + hostInfoLocal.getLocalIp()
                    + " localport=" + Constants.GAUSS_PORT + " localservice=" + Constants.GAUSS_LOCALSERVICE
                    + " remotehost=" + hostInfoTmp.getLocalIp() + " remoteport=" + Constants.GAUSS_PORT
                    + " remoteservice="
                    + Constants.GAUSS_LOCALSERVICE + "'");
            i++;
        }

        String configContent = readLinuxConfigContent(Constants.GAUSS_DATA_DIR + "/postgresql.conf");
        String[] configContentArray = configContent.split(Constants.LINE_SEPARATOR);

        StringBuilder sb = new StringBuilder();
        for (int j = 0; j < configContentArray.length; j++) {
            // configContentArray[j] 为空格，去掉
            if (StrUtil.isBlank(configContentArray[j])) {
                continue;
            }
            if (!configContentArray[j].startsWith("replconninfo")) {
                sb.append(configContentArray[j]).append(Constants.LINE_SEPARATOR);
            }
        }
        sb.append(String.join(Constants.LINE_SEPARATOR, replConnInfoList));
        writeConfiguration(Constants.GAUSS_DATA_DIR + "/postgresql.conf", sb.toString());
        // docker重启opengauss
        executeCommand("docker", "restart", "opengauss");
    }

    private void updateRedisAndSentinel(HostInfo hostInfo, String message) throws IOException, InterruptedException {
        if (message != null && message.contains(hostInfo.getLocalIp())) {
            log.info("本机{}发布的更新消息,updateRedisAndSentinel，不执行", hostInfo.getLocalIp());
            return;
        }
        String masterIp = getMasterIpFromSentinelOrLatestLiveIp(hostInfo);
        // Object o = redisUtils.redisGetKey(hostInfo.getLocalIp(),
        // Constants.REDIS_HOSTINFO_KEY);
        // size = 1 HOSTINFO::*************
        // 将o转为list
        // List<String> list = (List<String>)o;
        int sentinelNum = 1;
        // if (o != null && list.size() > 2) {
        // sentinelNum = list.size() / 2 + 1;
        // }
        String configContent = readConfigContent(Constants.SENTINEL_CONF).replace("${masterIp}", masterIp)
                .replace("${masterPort}", Constants.REDIS_PORT).replace("${masterAuth}", Constants.DEFAULT_PASSWORD)
                .replace("${sentinelNum}", sentinelNum + "");
        // writeConfiguration(Constants.SENTINEL_DIR + "/sentinel.conf", configContent);
        startSentinelContainer();
        redisMessageListener.resetSubscription();
    }

    /**
     * 添加服务健康检查工具方法
     *
     * @param serviceName
     * @param timeoutSeconds
     * @throws InterruptedException
     */
    private void checkServiceAvailability(String serviceName, int timeoutSeconds) throws InterruptedException {
        long startTime = System.currentTimeMillis();
        while (System.currentTimeMillis() - startTime < timeoutSeconds * 1000L) {
            try {
                // 示例：通过 Docker 检查容器是否健康运行
                Process checkProcess = new ProcessBuilder("docker", "inspect", "--format='{{.State.Running}}'",
                        serviceName).start();
                String output = readOutput(checkProcess);
                log.info("检查服务 {} 的健康状态：{}", serviceName, output);
                if (StrUtil.isNotBlank(output) && output.contains("true")) {
                    log.info("服务 {} 已启动", serviceName);
                    return;
                }
            } catch (IOException e) {
                // 忽略检查过程中的异常，继续重试
            }
            Thread.sleep(1000); // 每秒检查一次
        }
        throw new BusinessException("服务 " + serviceName + " 启动超时");
    }

    private void checkGaussAvailability(String localIp, String dbPassword, int timeoutSeconds)
            throws InterruptedException {
        long startTime = System.currentTimeMillis();
        while (System.currentTimeMillis() - startTime < timeoutSeconds * 1000L) {
            try {
                testDBConnection(localIp, dbPassword);
                log.info("opengauss 已启动");
                return;
            } catch (Exception e) {
                // 忽略检查过程中的异常，继续重试
            }
            Thread.sleep(1000); // 每秒检查一次
        }
        throw new BusinessException("opengauss服务启动超时");
    }

    @SneakyThrows
    public boolean isServiceReady(String containerName, String checkCommand) throws IOException {
        Process process = new ProcessBuilder("docker", "exec", containerName, "sh", "-c", checkCommand).start();
        return process.waitFor() == 0;
    }

    private String readOutput(Process process) throws IOException {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            return reader.lines().collect(Collectors.joining());
        }
    }

    public HostInfo testConnection(HostInfoTestDTO hostInfo) {
        // 先测试hostInfo.getIp()的连接
        if (NetworkUtils.isPortOpen(hostInfo.getMasterIp(), 22, Constants.HTTP_TIMEOUT)) {
            log.info("{} 端口{}已开启", hostInfo.getMasterIp(), 22);
        } else {
            log.error("{} 端口{}未开启", hostInfo.getMasterIp(), 22);
            throw new BusinessException(StrFormatter.format("{} 端口{}未开启", hostInfo.getMasterIp(), 22));
        }
        // 测试数据库连接
        if (NetworkUtils.isPortOpen(hostInfo.getMasterIp(), Integer.parseInt(Constants.GAUSS_LOCALSERVICE),
                Constants.HTTP_TIMEOUT)) {
            log.info("{} 端口{}已开启", hostInfo.getMasterIp(), Integer.parseInt(Constants.GAUSS_LOCALSERVICE));
        } else {
            log.error("{} 端口{}未开启", hostInfo.getMasterIp(), Integer.parseInt(Constants.GAUSS_LOCALSERVICE));
            throw new BusinessException(StrFormatter.format("{} 端口{}未开启", hostInfo.getMasterIp(),
                    Integer.parseInt(Constants.GAUSS_LOCALSERVICE)));
        }
        // 测试redis连接
        if (NetworkUtils.isPortOpen(hostInfo.getMasterIp(), Integer.parseInt(Constants.REDIS_PORT),
                Constants.HTTP_TIMEOUT)) {
            log.info("{} 端口{}已开启", hostInfo.getMasterIp(), Integer.parseInt(Constants.REDIS_PORT));
        } else {
            log.error("{} 端口{}未开启", hostInfo.getMasterIp(), Integer.parseInt(Constants.REDIS_PORT));
            throw new BusinessException(
                    StrFormatter.format("{} 端口{}未开启", hostInfo.getMasterIp(), Integer.parseInt(Constants.REDIS_PORT)));
        }
        String url = "https://" + hostInfo.getMasterIp() + ":" + Constants.SSO_PORT + "/system/queryCommonSysConfig";
        // String url = "https://*************:9000/system/queryCommonSysConfig";
        // 测试SSO服务是否正常
        if (NetworkUtils.checkHttpService(url)) {
            log.info("SSO服务正常");
        } else {
            log.error("SSO服务异常");
            throw new BusinessException("SSO服务异常：" + url);
        }
        HostInfo hostConfig = getHostConfig(hostInfo.getLocalIp());
        hostConfig.setRemoteIp(hostInfo.getMasterIp());
        if (StrUtil.isNotBlank(hostInfo.getLocalIp())) {
            addMasterInfo(hostConfig);
        }
        return hostConfig;
    }

    public void testSSHConnection(String host, String username, String password) {
        JSch jsch = new JSch();
        Session session = null;
        try {
            session = jsch.getSession(username, host, 22);

            session.setPassword(password);

            Properties config = new Properties();
            config.put("PreferredAuthentications", "password"); // 新增认证方式配置
            // 添加算法配置
            config.put("StrictHostKeyChecking", "no");
            config.put("kex",
                    "diffie-hellman-group-exchange-sha256,curve25519-sha256,<EMAIL>,diffie-hellman-group14-sha256");
            config.put("server_host_key", "rsa-sha2-512,rsa-sha2-256,ssh-ed25519,ecdsa-sha2-nistp256");
            config.put("cipher.s2c", "aes128-ctr,aes128-cbc,aes256-ctr,aes256-cbc");
            config.put("cipher.c2s", "aes128-ctr,aes128-cbc,aes256-ctr,aes256-cbc");
            config.put("mac.s2c", "hmac-sha2-256,hmac-sha1");
            config.put("mac.c2s", "hmac-sha2-256,hmac-sha1");
            session.setConfig(config);

            // 设置连接超时时间为10秒
            session.connect(10000);

            // 测试系统信息
            if (session.isConnected()) {
                //// 获取系统信息
                // String osInfo = executeCommand(session, "uname -a");
                // result.put(type + "OsInfo", osInfo);
                //// 获取内存信息
                // String memInfo = executeCommand(session, "free -h | grep Mem");
                // result.put(type + "MemInfo", memInfo);
                //// 获取磁盘信息
                // String diskInfo = executeCommand(session, "df -h | grep /$ | awk '{print
                //// $5}'");
                // result.put(type + "DiskUsage", diskInfo);
            }
        } catch (Exception e) {
            throw new BusinessException("连接失败: " + e.getMessage());
        } finally {
            if (session != null && session.isConnected()) {
                session.disconnect();
            }
        }
    }

    public void testDBConnection(String host, String password) {
        // 加载OpenGauss驱动
        try {
            Class.forName("org.postgresql.Driver");
        } catch (ClassNotFoundException e) {
            throw new BusinessException("OpenGauss驱动加载失败: " + e.getMessage());
        }

        Properties props = new Properties();
        props.setProperty("user", Constants.GAUSS_USERNAME);
        props.setProperty("password", password);
        // OpenGauss特定的连接参数
        props.setProperty("connectTimeout", "10");
        props.setProperty("loginTimeout", "10");
        props.setProperty("socketTimeout", "10");
        props.setProperty("ApplicationName", "OpenGauss-HA-Monitor");

        try (Connection conn = DriverManager.getConnection(String.format("**********************************", host),
                props)) {
            try (Statement stmt = conn.createStatement()) {
                ResultSet rs = stmt.executeQuery("SELECT 1");
                if (!rs.next()) {
                    throw new BusinessException("数据库连接测试失败：无法执行查询");
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException("数据库连接失败: " + e.getMessage());
        }
    }

    public void gaussRunSql(String host, String password, String sql) {
        // 加载OpenGauss驱动
        try {
            Class.forName("org.postgresql.Driver");
        } catch (ClassNotFoundException e) {
            throw new BusinessException("OpenGauss驱动加载失败: " + e.getMessage());
        }

        Properties props = new Properties();
        props.setProperty("user", Constants.GAUSS_USERNAME);
        props.setProperty("password", password);
        // OpenGauss特定的连接参数
        props.setProperty("connectTimeout", "10");
        props.setProperty("loginTimeout", "10");
        props.setProperty("socketTimeout", "10");
        props.setProperty("ApplicationName", "OpenGauss-HA-Monitor");

        try (Connection conn = DriverManager.getConnection(String.format("**********************************", host),
                props)) {
            try (Statement stmt = conn.createStatement()) {
                Boolean result = stmt.execute(sql);
                if (result) {
                    throw new BusinessException("数据库连接测试失败：无法执行查询");
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException("数据库连接失败: " + e.getMessage());
        }
    }

    // 检查权限，当前用户是不是超级管理员
    @SneakyThrows
    public void softRestore(HostInfoDTO hostInfo) {
        // 直接执行 cleanup.sh脚本
        executeCommand("bash", "/home/<USER>/icm/cleanup.sh");

        // executeCommand("rm", "-rf", "/home/<USER>/icm/config/");
        // executeCommand("rm", "-rf", "/home/<USER>/icm/data.json/");
        // executeCommand("rm", "-rf", "/home/<USER>/icm/icm.log/");
        // executeCommand("rm", "-rf", "/home/<USER>/gauss5/data/");
        // executeCommand("docker", "rm", "-f", "redis", "sentinel", "opengauss", "sso",
        // "nginxsso");
        // executeCommand("rm", "-rf", "/home/<USER>/nginx/logs/");
        // executeCommand("rm", "-rf", "/home/<USER>/redis/data/");
        // executeCommand("rm", "-rf", "/home/<USER>/");
    }

    public void updateOpenGaussPassword(UpdateGaussPasswordRequest gaussPasswordRequest) {
        // 设置的新密码
        String dbNewPassword = gaussPasswordRequest.getDbPassword();
        HostInfo hostConfig = getHostConfig(null);
        if (hostConfig == null) {
            throw new BusinessException("未找到有效的主机配置");
        }
        // 旧密码
        String oldDbPassword = AESUtils.isAESEnCode(gaussPasswordRequest.getOldDbPassword())
                ? gaussPasswordRequest.getOldDbPassword()
                : AESUtils.AESEnCode(gaussPasswordRequest.getOldDbPassword());
        ;
        if (!Objects.equals(oldDbPassword, hostConfig.getDbPassword())) {
            throw new BusinessException("旧密码错误");
        }

        String dbNewPasswordNormal = AESUtils.isAESEnCode(dbNewPassword) ? AESUtils.AESDeCode(dbNewPassword)
                : dbNewPassword;
        String dbOldPasswordNormal = AESUtils.AESDeCode(oldDbPassword);
        // 更新数据库密码
        // 校验密码：至少包含1 个大写字母、1 个小写字母、1 个数字、1 个特殊字符，四类字符中的三类，密码长度 8~32
        // 字符，不能和用户名、用户名倒写相同，本要求为非大小写敏感，不能和当前密码、当前密码的倒写相同
        boolean validatePassword = PasswordUtils.validatePassword(dbNewPasswordNormal, Constants.GAUSS_USERNAME,
                dbOldPasswordNormal);
        if (!validatePassword) {
            throw new BusinessException("密码不符合要求");
        }
        // 先通知关闭所有sso服务，防止密码错误用户被锁
        List<String> hostKeyList = getHostKeyList(hostConfig);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("secretKey", secretKey);
        jsonObject.put("serverName", "sso");
        String normalIp;
        String localIp = hostConfig.getLocalIp();
        stopService(JSONUtil.toBean(jsonObject, ServiceRequest.class));
        try {
            for (String hostKey : hostKeyList) {
                // 去掉HOSTINFO::
                normalIp = hostKey.substring(Constants.REDIS_HOSTINFO_KEY.length() + 2);
                if (localIp.equals(normalIp)) {
                    continue;
                }
                // 发送http请求关闭sso服务，使用hutool工具类
                String url = "http://" + normalIp + ":" + serverPort + "/api/common/stopService";
                try {
                    HttpUtil.post(url, jsonObject.toString(), 10000);
                } catch (Exception e) {
                    log.error("关闭sso服务失败：{}", e.getMessage());
                }
            }
            String masterIp = getMasterIpFromSentinelOrLatestLiveIp(hostConfig);
            // 更新数据库密码
            gaussRunSql(masterIp, hostConfig.getDbPassword(),
                    "alter user gaussdb identified by '" + dbNewPasswordNormal + "' replace '" + dbOldPasswordNormal
                            + "'");
        } catch (Exception e) {
            startService(JSONUtil.toBean(jsonObject, ServiceRequest.class));
            for (String hostKey : hostKeyList) {
                // 去掉HOSTINFO::
                normalIp = hostKey.substring(Constants.REDIS_HOSTINFO_KEY.length() + 2);
                if (localIp.equals(normalIp)) {
                    continue;
                }
                // 发送http请求关闭sso服务，使用hutool工具类
                String url = "http://" + normalIp + ":" + serverPort + "/api/common/startService";
                try {
                    HttpUtil.post(url, jsonObject.toString(), 10000);
                } catch (Exception e2) {
                    log.error("关闭sso服务失败：{}", e2.getMessage());
                }
            }
        }
        // dealUpdateGaussAndSso(hostConfig, dbNewPasswordNormal);
        redisUtils.publish(getMasterIp(hostConfig), Constants.REDIS_UPDATE_NOTICE_KEY,
                Constants.UPDATE_GAUSS_PASSWORD + ":" + JSONUtil.toJsonStr(gaussPasswordRequest));
    }

    public void dealUpdateGaussAndSso(HostInfo hostConfig, String dbNewPassword) {
        log.info("开始更新sso密码");
        // hostInfo转化为Map
        hostConfig
                .setDbPassword(AESUtils.isAESEnCode(dbNewPassword) ? dbNewPassword : AESUtils.AESEnCode(dbNewPassword));
        Map<String, Object> map = BeanUtil.beanToMap(hostConfig);
        jsonDbsonManager.updateRow(hostConfig.getId(), map);
        addInfoToRedis(hostConfig, false);

        updateSso(hostConfig, new ArrayList<>());
        log.info("更新sso密码完成");
    }

    @SneakyThrows
    public void stopService(ServiceRequest serviceRequest) {
        executeCommand("docker", "stop", serviceRequest.getServerName());
    }

    @SneakyThrows
    public void startService(ServiceRequest serviceRequest) {
        executeCommand("docker", "restart", serviceRequest.getServerName());
    }

    public void updateVip(UpdateVipRequest updateVipRequest) {
        HostInfo hostConfig = getHostConfig(null);
        if (hostConfig == null) {
            throw new BusinessException("未找到有效的主机配置");
        }
        hostConfig.setVipIp(updateVipRequest.getVipIp());
        log.info("发布更新Keepalived服务通知----------");
        redisUtils.publish(getMasterIp(hostConfig), Constants.REDIS_UPDATE_NOTICE_KEY,
                Constants.UPDATE_VIP + ":" + JSONUtil.toJsonStr(updateVipRequest));
    }

    public List<HostInfo> getAll(String ip) {
        List<String> hostKeyList = getHostKeyList(getHostConfig(ip));
        List<HostInfo> hostInfoList = new ArrayList<>();
        for (String hostKey : hostKeyList) {
            hostInfoList.add(JSONUtil.toBean(StringUtils.getValue(redisUtils.redisGet(ip, hostKey)), HostInfo.class));
        }
        return hostInfoList;
    }

    public List<HostInfo> getAllSlave(String ip) {
        List<String> hostKeyList = getHostKeyList(getHostConfig(ip));
        List<HostInfo> hostInfoList = new ArrayList<>();
        HostInfo hostInfo;
        String hostInfoStr;
        for (String hostKey : hostKeyList) {
            hostInfoStr = StringUtils.getValue(redisUtils.redisGet(ip, hostKey));
            if (StrUtil.isBlank(hostInfoStr)) {
                continue;
            }
            hostInfo = JSONUtil.toBean(hostInfoStr, HostInfo.class);
            if (Constants.HOST_TYPE_MASTER.equals(hostInfo.getHostType())) {
                continue;
            }
            hostInfoList.add(hostInfo);
        }
        return hostInfoList;
    }

    /**
     * 根据种子生成复杂密码
     *
     * @param seed 随机种子
     * @return 符合复杂度要求的密码
     */
    private static String generateComplexPassword(String seed) {
        // 特殊字符集合（只包含验证正则允许的字符）
        String specialChars = "@$!%*?&";
        // 使用种子的哈希值作为基础
        int hashCode = Math.abs(seed.hashCode());

        // 确保密码长度为16位
        int length = 16;

        // 多次尝试生成符合要求的密码
        for (int attempt = 0; attempt < 10; attempt++) {
            StringBuilder password = new StringBuilder();
            int currentHashCode = hashCode + attempt * 1000;

            // 添加至少一个大写字母
            password.append((char) ('A' + currentHashCode % 26));

            // 添加至少一个小写字母
            password.append((char) ('a' + (currentHashCode % 17) % 26));

            // 添加至少一个数字
            password.append((char) ('0' + currentHashCode % 10));

            // 添加至少一个特殊字符
            password.append(specialChars.charAt(currentHashCode % specialChars.length()));

            // 填充剩余位置，确保随机性
            for (int i = password.length(); i < length; i++) {
                int charType = (currentHashCode + i) % 4;
                switch (charType) {
                    case 0: // 大写字母
                        password.append((char) ('A' + (currentHashCode + i * 13) % 26));
                        break;
                    case 1: // 小写字母
                        password.append((char) ('a' + (currentHashCode + i * 17) % 26));
                        break;
                    case 2: // 数字
                        password.append((char) ('0' + (currentHashCode + i * 7) % 10));
                        break;
                    case 3: // 特殊字符
                        password.append(specialChars.charAt((currentHashCode + i * 11) % specialChars.length()));
                        break;
                }
            }

            // 随机打乱密码中的字符顺序，增加随机性
            char[] charArray = password.toString().toCharArray();
            for (int i = 0; i < charArray.length; i++) {
                int j = (currentHashCode + i * 31) % charArray.length;
                char temp = charArray[i];
                charArray[i] = charArray[j];
                charArray[j] = temp;
            }

            String finalPassword = new String(charArray);

            // 验证生成的密码是否符合要求
            if (PasswordUtils.validatePassword(finalPassword, Constants.GAUSS_USERNAME, "")) {
                System.out.println("生成的复杂密码：" + finalPassword);
                return finalPassword;
            }
        }

        // 如果多次尝试都失败，使用固定的符合要求的密码模板
        String fallbackPassword = Constants.DEFAULT_PASSWORD + (hashCode % 1000000);
        System.out.println("使用备用密码：" + fallbackPassword);
        return fallbackPassword;
    }

    public void deployMinioMaster(HostInfo hostConfig, List<String> list) {
        // mc alias remove minio_master
        // mc alias remove minio_slave
        try {
            // 检查并删除 minio_master 别名
            if (isAliasExists("minio_master")) {
                executeCommand("mc", "alias", "remove", "minio_master");
            }

            // 检查并删除 minio_slave 别名
            if (isAliasExists("minio_slave")) {
                executeCommand("mc", "alias", "remove", "minio_slave");
            }
        } catch (Exception e) {
            log.error("部署MinIO Master服务错误", e);
        }
    }

    // 优化，如果没有初始化等初始化好再启动
    public void deployMinioSlave(HostInfo hostConfig, List<String> list) {
        // mc alias remove minio_master
        // mc alias remove minio_slave
        try {
            // executeCommand("mc", "alias", "remove", "minio_master");
            // executeCommand("mc", "alias", "remove", "minio_slave");
            // 设置主备同步（使用配置文件中的认证信息）
            executeCommand("mc", "alias", "set", "minio_master", "http://" + hostConfig.getRemoteIp() + ":9001",
                    minioAccessKey, minioSecretKey);
            executeCommand("mc", "alias", "set", "minio_slave", "http://" + hostConfig.getLocalIp() + ":9001",
                    minioAccessKey, minioSecretKey);
            // 使用 --watch 会一直前台运行导致当前线程阻塞，这里放到后台执行避免接口不返回
            executeCommand("sh", "-c",
                    "nohup mc mirror --remove --overwrite --watch minio_master minio_slave > /home/<USER>/mirror.log 2>&1 &");
        } catch (Exception e) {
            log.error("部署MinIO Slave服务错误", e);
        }
    }

    private boolean isAliasExists(String aliasName) throws IOException, InterruptedException {
        Process process = new ProcessBuilder("mc", "alias", "list").start();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.contains(aliasName)) {
                    return true;
                }
            }
        }
        return false;
    }
}
