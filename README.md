# ICM (Initialization, Configuration, Monitoring)

ICM 是一个基于 Spring Boot 的综合运维管理平台，主要提供以下核心功能：

1. 可视化配置界面：用于初始化实体机配置
2. 数据库集群监控：实时监控 OpenGauss 数据库集群状态
3. 自动故障转移：在检测到故障时自动执行主备切换
4. 脚本管理系统：统一管理和执行初始化相关的所有脚本（开发中）

注意事项：
1、充分阅读之前的代码；
1、响应和请求和当前其他模块保持一致，代码风格保持一致，代码结构保持一致；
2、尽可能小的修改代码；
3、完整的注释；

## 1. 系统架构

### 1.1 技术栈

- 后端：Spring Boot 2.x
- 前端：Vue.js 3.x + Element Plus
- 数据库：OpenGauss 5.0.3
- 缓存：Redis
- 容器化：Docker

### 1.2 系统模块

- 配置管理模块：提供 Web 界面进行系统配置
- 监控模块：负责数据库集群状态监控
- 故障转移模块：执行自动故障检测和转移
- 脚本管理模块：管理初始化脚本（开发中）
- 文件存储模块：基于 MinIO 的对象存储服务

## 2. 系统要求

### 2.1 硬件要求

- CPU: 2 核或以上
- 内存: 4GB 或以上
- 磁盘空间: 20GB 或以上

### 2.2 软件要求

- 操作系统: openEuler 24.03 LTS
- Java 环境: JDK 8 或以上
- Docker: 最新稳定版
- OpenGauss: 5.0.3 (Docker 版本)
- Redis: 最新稳定版
- MinIO: 最新稳定版（用于文件存储）

## 3. 功能特性

### 3.1 配置管理界面

- 可视化配置实体机参数
- 支持批量导入配置
- 配置模板管理
- 配置历史记录和回滚
- 多环境配置管理

### 3.2 数据库集群监控

- 实时监控 OpenGauss 主备节点状态
- 性能指标监控（CPU、内存、磁盘等）
- 数据库关键指标监控
- 告警阈值自定义
- 监控数据可视化展示

### 3.3 自动故障转移

- 自动故障检测和故障转移
- 支持动态添加备节点
- 分布式锁保证操作安全
- 自动恢复原主节点为备节点
- 故障转移日志记录

### 3.4 脚本管理（开发中）

- 脚本在线编辑
- 脚本版本控制
- 脚本执行记录
- 定时任务管理
- 脚本权限管理

### 3.5 系统升级功能

- 支持压缩文件上传管理（基于 MinIO 对象存储）
- 支持文件 MD5 校验，确保文件完整性
- 支持立即升级和定时升级
- 保留上一个版本的升级文件，支持后续回退功能
- 提供历史升级日志记录与查询，记录从哪个版本升级到哪个版本，升级时间和升级文件名称等信息
- 区分升级和回退操作日志，通过 operationType 字段标识（0-升级，1-回退）
- 记录旧版本信息，支持版本回退追溯
- 使用 MinIO 对象存储，提供高可用的文件存储服务
- 集成 x-file-storage-spring，提供统一的文件存储接口

#### 3.5.1 升级和回退功能

系统支持两种类型的回退操作：

1. **全量回退**：回退整个系统到上一个版本

   - API: `/api/upgrade/rollback`
   - 方法: POST
   - 自动分析当前版本包含的文件，回退到对应的上一个版本
   - 会重启服务器

2. **文件级回退**：回退指定文件到包含该文件的上一个版本
   - API: `/api/upgrade/rollback/file`
   - 方法: POST
   - 请求参数: `fileName`（文件名称）
   - 仅回退指定文件到包含该文件的上一个版本
   - 如果回退的是核心文件，会重启服务器

所有升级和回退操作都会在日志中记录，通过 `operationType` 字段区分：

- `operationType = 0`: 升级操作
- `operationType = 1`: 回退操作

##### 上传升级包

- URL: `/api/upgrade/upload`
- 方法: POST
- 请求参数:
  - `file`: 升级包文件（必填）
  - `name`: 升级包名称（必填）
  - `version`: 升级包版本（必填）
  - `md5`: 文件 MD5 值（可选，用于校验文件完整性）
  - `forceAssociation`: 是否强关联（可选，默认 false）
  - `description`: 描述信息（可选）
- 响应:
  ```json
  {
    "code": 0,
    "msg": "上传成功",
    "data": "升级包ID"
  }
  ```

##### 创建升级任务

- URL: `/api/upgrade/create-task`
- 方法: POST
- 请求参数:
  - `packageId`: 升级包 ID（必填）
  - `upgradeType`: 升级类型（必填，0-立即升级，1-定时升级）
  - `scheduledTime`: 计划升级时间（当 upgradeType=1 时必填，格式：yyyy-MM-dd HH:mm:ss）
  - `description`: 描述信息（可选）
- 响应:
  ```json
  {
    "code": 0,
    "msg": "创建成功",
    "data": "任务ID"
  }
  ```

##### 获取升级包列表

- URL: `/api/upgrade/packages`
- 方法: GET
- 响应:
  ```json
  {
    "code": 0,
    "msg": "获取成功",
    "data": [
      {
        "id": "升级包ID",
        "name": "升级包名称",
        "version": "版本号",
        "md5": "MD5值",
        "forceAssociation": false,
        "uploadTime": "上传时间",
        "description": "描述信息"
      }
    ]
  }
  ```

##### 获取升级任务列表

- URL: `/api/upgrade/tasks`
- 方法: GET
- 响应:
  ```json
  {
    "code": 0,
    "msg": "获取成功",
    "data": [
      {
        "id": "任务ID",
        "upgradePackageId": "升级包ID",
        "upgradeType": 0,
        "scheduledTime": "计划升级时间",
        "status": 0,
        "createTime": "创建时间",
        "executeTime": "执行时间",
        "completeTime": "完成时间",
        "remark": "备注"
      }
    ]
  }
  ```

##### 获取升级包详情

- URL: `/api/upgrade/packages/{id}`
- 方法: GET
- 响应:
  ```json
  {
    "code": 0,
    "msg": "获取成功",
    "data": {
      "id": "升级包ID",
      "name": "升级包名称",
      "version": "版本号",
      "md5": "MD5值",
      "forceAssociation": false,
      "uploadTime": "上传时间",
      "description": "描述信息"
    }
  }
  ```

##### 获取升级任务详情

- URL: `/api/upgrade/tasks/{id}`
- 方法: GET
- 响应:
  ```json
  {
    "code": 0,
    "msg": "获取成功",
    "data": {
      "id": "任务ID",
      "upgradePackageId": "升级包ID",
      "upgradeType": 0,
      "scheduledTime": "计划升级时间",
      "status": 0,
      "createTime": "创建时间",
      "executeTime": "执行时间",
      "completeTime": "完成时间",
      "remark": "备注"
    }
  }
  ```

##### 立即执行升级任务

- URL: `/api/upgrade/tasks/{id}/execute`
- 方法: POST
- 响应:
  ```json
  {
    "code": 0,
    "msg": "执行成功"
  }
  ```

##### 获取当前版本信息

- URL: `/api/upgrade/current`
- 方法: GET
- 响应:
  ```json
  {
    "code": 0,
    "msg": "获取成功",
    "data": {
      "id": "升级包ID",
      "name": "升级包名称",
      "version": "版本号",
      "md5": "MD5值",
      "forceAssociation": false,
      "uploadTime": "上传时间",
      "description": "描述信息"
    }
  }
  ```

##### 获取上一个版本信息

- URL: `/api/upgrade/previous`
- 方法: GET
- 响应:
  ```json
  {
    "code": 0,
    "msg": "获取成功",
    "data": {
      "id": "升级包ID",
      "name": "升级包名称",
      "version": "版本号",
      "md5": "MD5值",
      "forceAssociation": false,
      "uploadTime": "上传时间",
      "description": "描述信息"
    }
  }
  ```

##### 版本回退

- URL: `/api/upgrade/rollback`
- 方法: POST
- 响应:
  ```json
  {
    "code": 0,
    "msg": "回退操作已执行，服务器将在10秒后重启"
  }
  ```
- 说明:
  - 系统会自动分析当前版本包含的所有文件类型（如 icm-*、SSO-*等）
  - 对每种文件类型，分别查找包含该类型文件的最近一个历史升级包
  - 智能回退每种文件到对应的上一个版本，而不是简单回退整个升级包
  - 回退操作会重启服务器，请确保在合适的时间执行
  - 回退操作会自动记录升级日志，包括成功和失败的文件类型
  - ICM 相关文件（如执行回退的程序本身）会使用特殊的回退方式，确保回退过程的安全性
  - 对于 ICM 文件，系统会使用异步方式进行回退，避免在程序运行时替换程序文件
  - 回退过程中会将当前版本保存为新的上一个版本，以便可以再次回退
  - 如果当前版本不包含任何文件，或者所有文件都无法找到历史版本，操作将失败

##### 按文件回退版本

- URL: `/api/upgrade/rollback/file`
- 方法: POST
- 请求参数:
  - `fileName`: 需要回退的文件名（必填，例如"icm-xxx"）
- 响应:
  ```json
  {
    "code": 0,
    "msg": "文件回退操作已执行，服务器将在10秒后重启"
  }
  ```
- 说明:
  - 此接口用于回退特定文件到包含该文件的上一个版本
  - 系统会智能查找包含该文件的最近一个历史升级包
  - 回退只会替换指定文件，而不是整个升级包
  - 回退操作会重启服务器，请确保在合适的时间执行
  - 回退操作会自动记录升级日志
  - 如果当前版本不包含指定文件，或者找不到包含该文件的历史版本，操作将失败

##### 取消定时升级任务

- URL: `/api/upgrade/tasks/{taskId}/cancel`
- 方法: POST
- 请求参数:
  - `taskId`: 任务 ID（必填，路径参数）
- 响应:
  ```json
  {
    "code": 0,
    "msg": "取消成功"
  }
  ```

##### 删除升级包

- URL: `/api/upgrade/packages/{packageId}`
- 方法: DELETE
- 请求参数:
  - `packageId`: 升级包 ID（必填，路径参数）
- 响应:
  ```json
  {
    "code": 0,
    "msg": "删除成功"
  }
  ```
- 说明:
  - 如果升级包有正在执行的升级任务，将无法删除
  - 删除升级包时，相关的待执行定时任务会被自动取消
  - 删除操作会同时删除升级包文件和 JSON 记录

##### 获取升级历史日志列表

- URL: `/api/upgrade/logs`
- 方法: GET
- 响应:
  ```json
  {
    "code": 0,
    "msg": "获取成功",
    "data": [
      {
        "id": "日志ID",
        "fileName": "升级包文件名称",
        "fromVersion": "来源版本",
        "toVersion": "目标版本",
        "upgradeTime": "升级时间",
        "operator": "操作人员",
        "result": 0,
        "errorMessage": "错误信息",
        "remark": "备注信息",
        "taskId": "关联的任务ID",
        "packageId": "关联的升级包ID",
        "operationType": 0 // 操作类型：0-升级，1-回退
      }
    ]
  }
  ```

##### 获取升级历史日志详情

- URL: `/api/upgrade/logs/{id}`
- 方法: GET
- 响应:
  ```json
  {
    "code": 0,
    "msg": "获取成功",
    "data": {
      "id": "日志ID",
      "fileName": "升级包文件名称",
      "fromVersion": "来源版本",
      "toVersion": "目标版本",
      "upgradeTime": "升级时间",
      "operator": "操作人员",
      "result": 0,
      "errorMessage": "错误信息",
      "remark": "备注信息",
      "taskId": "关联的任务ID",
      "packageId": "关联的升级包ID",
      "operationType": 0 // 操作类型：0-升级，1-回退
    }
  }
  ```

## 4. 认证说明

### 4.1 基于 Token 的认证机制

系统使用基于 Token 的认证机制，认证流程如下：

1. 用户通过 `/api/auth/login` 接口提交用户名和密码进行登录
2. 服务器验证用户名和密码正确后，生成一个 Token 并返回给客户端
3. 客户端在后续请求中，通过 HTTP 请求头 `Authorization` 携带该 Token
4. 服务器通过验证 Token 的有效性来确认用户身份

#### 4.1.1 Token 认证特点

- Token 直接存储在服务器内存中，重启服务会导致所有 Token 失效，用户需重新登录
- 相比 Session，无需依赖 Cookie，更适合 API 和移动端应用
- Token 默认有效期为 120 分钟，超过时间后自动失效
- 系统会在用户每次访问 API 时自动刷新 Token 的过期时间
- 服务器会定期清理过期 Token，提高系统性能
- 用户退出时需显式调用登出接口销毁 Token

#### 4.1.2 API 认证示例

**登录请求示例：**

```
POST /api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "password"
}
```

**登录响应示例：**

```json
{
  "status": 0,
  "msg": "操作成功",
  "data": {
    "username": "admin",
    "role": "ADMIN",
    "needChangePassword": false,
    "token": "生成的认证Token",
    "loginIp": "127.0.0.1"
  }
}
```

**使用 Token 访问受保护资源示例：**

```
GET /api/some-protected-resource
Authorization: 生成的认证Token
```

**登出请求示例：**

```
POST /api/auth/logout
Authorization: 生成的认证Token
```

## 5. 部署步骤

### 5.1 安装基础环境

1. 安装 JDK：

```bash
dnf install java-1.8.0-openjdk-devel
```

2. 安装 Docker：

```bash
dnf install docker
systemctl enable docker
systemctl start docker
```

3. 安装 Redis：

```bash
dnf install redis
systemctl enable redis
systemctl start redis
```

### 5.2 部署 OpenGauss

1. 创建数据目录：

```bash
mkdir -p /home/<USER>/gauss5
```

2. 部署主节点：

```bash
docker run --network=host --privileged=true \
    --name opengauss_master -h opengauss_master \
    -d -e GS_PORT=5432 \
    -e OG_SUBNET=opengaussnetwork \
    -e GS_PASSWORD=Jykj1994@ \
    -e NODE_NAME=opengauss_master \
    -e REPL_CONN_INFO="replconninfo1 = 'localhost=*************** localport=5434 localservice=5432 remotehost=*************** remoteport=5434 remoteservice=5432'\n" \
    -v /home/<USER>/gauss5:/var/lib/opengauss \
    enmotech/opengauss:5.0.3 -M primary
```

3. 部署备节点（在备机上执行）：

```bash
docker run --network=host --privileged=true \
    --name opengauss_slave1 -h opengauss_slave1 \
    -d -e GS_PORT=5432 \
    -e OG_SUBNET=opengaussnetwork \
    -e GS_PASSWORD=Jykj1994@ \
    -e NODE_NAME=opengauss_slave1 \
    -e REPL_CONN_INFO="replconninfo1 = 'localhost=*************** localport=5434 localservice=5432 remotehost=*************** remoteport=5434 remoteservice=5432'\n" \
    -v /home/<USER>/gauss5:/var/lib/opengauss \
    enmotech/opengauss:5.0.3 -M standby
```

### 5.3 部署监控系统

1. 创建必要目录：

```bash
mkdir -p /opt/opengauss
mkdir -p /var/log/opengauss_monitor
```

2. 编译打包：

```bash
mvn clean package
```

3. 复制文件：

```bash
cp target/ha-monitor-1.0-SNAPSHOT.jar /opt/opengauss/
cp src/main/resources/application.yml /opt/opengauss/
```

4. 创建服务配置：

```bash
cat > /etc/systemd/system/opengauss-monitor.service << EOF
[Unit]
Description=OpenGauss HA Monitor Service
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/opt/opengauss
Environment="LOCAL_HOST=***************"
Environment="REDIS_HOST=*************"
ExecStart=/usr/bin/java -jar /opt/opengauss/ha-monitor-1.0-SNAPSHOT.jar
Restart=always
RestartSec=30

[Install]
WantedBy=multi-user.target
EOF
```

5. 启动服务：

```bash
systemctl daemon-reload
systemctl enable opengauss-monitor
systemctl start opengauss-monitor
```

### 5.4 验证部署

1. 检查服务状态：

```bash
systemctl status opengauss-monitor
```

2. 查看日志：

```bash
tail -f /var/log/opengauss_monitor/opengauss_monitor.log
```

### 5. 网关服务配置管理

### 5.1 网关配置功能

系统提供了对网关服务的配置管理功能，主要包括：

1. 自动更新网关配置文件

   - 更新 VPN 服务器配置：`/usr/local/soft/vpn_server_v2/config.yml`
   - 更新 VPN 管理服务配置：`/usr/local/soft/vpn_manage_v2/config.json`
   - 更新 VPN 管理数据库配置：`/usr/local/soft/vpn_manage_v2/dbconfig.yml`

2. 提供统一的服务管理脚本
   - 脚本路径：`/usr/local/soft/services.sh`
   - 支持启动、停止、重启网关服务

### 5.2 配置文件说明

#### 5.2.1 VPN 服务器配置 (`/usr/local/soft/vpn_server_v2/config.yml`)

此配置文件主要包含 Redis 和 PostgreSQL 的连接信息：

```yaml
redis:
  host: 127.0.0.1 # Redis服务器IP
  port: 6379 # Redis端口
  password: "密码" # Redis密码
  db: 0 # Redis数据库索引

postgresql:
  host: 127.0.0.1 # PostgreSQL服务器IP
  port: 5432 # PostgreSQL端口
  user: netguard # 数据库用户名
  password: "密码" # 数据库密码
  database: netguard_db # 数据库名称
```

#### 5.2.2 VPN 管理服务配置 (`/usr/local/soft/vpn_manage_v2/config.json`)

此配置文件主要包含服务器 IP 和网卡名称等信息：

```json
{
  "version": "1",
  "host": "*************",  # 服务器IP
  "port": 50121,
  "auto_ssl": false,
  "password": "netguard",
  "lang": "en",
  "log_level": "error",
  "wg_private_key": "YK/3Twt8G5RxALVNSz2V2aVH1xC0iE4RDaCS0u+LWX8=",
  "wg_device": "enp125s0f0",  # 网卡名称
  "wg_port": 50120,  # WireGuard端口
  "wg_mtu": 1280,
  "wg_persistent_keepalive": 25,
  "wg_address": "**********/16",
  "wg_dns": "*******",
  "wg_allowed_ips": "0.0.0.0/0, ::/0"
}
```

#### 5.2.3 VPN 管理数据库配置 (`/usr/local/soft/vpn_manage_v2/dbconfig.yml`)

此配置文件包含数据库和 Redis 连接信息：

```yaml
db:
  host: 127.0.0.1 # 数据库服务器IP
  port: 5432 # 数据库端口
  user: netguard # 数据库用户名
  password: "密码" # 数据库密码
  sslMode: disable # SSL模式
  dbName: netguard_db # 数据库名称

redis:
  host: 127.0.0.1 # Redis服务器IP
  port: 6379 # Redis端口
  password: "密码" # Redis密码
  db: 0 # Redis数据库索引
```

### 5.3 服务管理命令

使用以下命令管理网关服务:

1. 启动网关服务:

   ```
   sh /usr/local/soft/services.sh start
   ```

2. 停止网关服务:

   ```
   sh /usr/local/soft/services.sh stop
   ```

3. 重启网关服务:
   ```
   sh /usr/local/soft/services.sh restart
   ```

注意：服务管理命令需要使用 root 权限执行。

### 5.4 配置更新流程

网关配置更新主要在以下几种情况下自动进行:

1. 主服务部署时
2. 系统配置更新时
3. 通过 API 手动触发配置更新

配置更新过程会自动备份原配置文件，并根据当前系统状态和主机信息更新新的配置。

## 6. 配置说明

主要配置文件 application.yml：

```yaml
spring:
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}

# MinIO文件存储配置
dromara:
  x-file-storage:
    default-platform: minio-1
    thumbnail-suffix: ".min.jpg"
    minio:
      - platform: minio-1
        enable-storage: true
        access-key: ${MINIO_ACCESS_KEY:minioadmin}
        secret-key: ${MINIO_SECRET_KEY:minioadmin}
        end-point: ${MINIO_ENDPOINT:http://localhost:9000}
        bucket-name: ${MINIO_BUCKET:icm-files}
        domain: ${MINIO_DOMAIN:http://localhost:9000}
        base-path: ${MINIO_BASE_PATH:/}

monitor:
  local:
    host: ${LOCAL_HOST:***************}
  primary:
    host: ***************
    container: opengauss_master
  standby:
    nodes:
      - host: ***************
        container: opengauss_slave1
  database:
    port: 5432
    user: gaussdb
    password: Jykj1994@
    data-dir: /home/<USER>/gauss5
  cluster:
    check-interval: 30
```

### 6.1 MinIO 配置参数说明

| 参数        | 环境变量         | 默认值                | 说明           |
| ----------- | ---------------- | --------------------- | -------------- |
| access-key  | MINIO_ACCESS_KEY | minioadmin            | MinIO 访问密钥 |
| secret-key  | MINIO_SECRET_KEY | minioadmin            | MinIO 秘密密钥 |
| end-point   | MINIO_ENDPOINT   | http://localhost:9000 | MinIO 服务端点 |
| bucket-name | MINIO_BUCKET     | icm-files             | 存储桶名称     |
| domain      | MINIO_DOMAIN     | http://localhost:9000 | 访问域名       |
| base-path   | MINIO_BASE_PATH  | /                     | 基础路径       |

### 6.2 环境变量配置示例

在生产环境中，建议通过环境变量配置 MinIO 连接信息：

```bash
export MINIO_ACCESS_KEY=your_access_key
export MINIO_SECRET_KEY=your_secret_key
export MINIO_ENDPOINT=http://your-minio-server:9000
export MINIO_BUCKET=icm-files
export MINIO_DOMAIN=http://your-minio-server:9000
```

### 6.3 MinIO 连接测试

应用启动后，可以使用以下 API 测试 MinIO 连接：

1. **测试连接状态**

   - URL: `/api/minio-test/connection`
   - 方法: GET
   - 说明: 测试 MinIO 服务器连接状态和 bucket 信息

2. **测试文件上传**

   - URL: `/api/minio-test/upload-test`
   - 方法: POST
   - 说明: 测试文件上传功能

3. **清理测试文件**
   - URL: `/api/minio-test/cleanup`
   - 方法: DELETE
   - 说明: 删除测试文件

### 6.4 自动初始化

应用启动时会自动：

- 检查 MinIO 连接
- 创建配置的 bucket（如果不存在）
- 设置 bucket 为公共读取权限
- 验证文件存储功能

**注意**: 应用会自动将 bucket 设置为公共读取权限，以便文件可以通过 URL 直接访问。如果设置失败，请手动在 MinIO 控制台中设置 bucket 权限。

## 7. 维护操作

### 7.1 添加新的备节点

1. 在新节点上部署 OpenGauss 备机
2. 修改主节点的配置文件，添加新的备节点信息
3. 重启监控服务

### 7.2 日志管理

```bash
# 查看日志
tail -f /var/log/opengauss_monitor/opengauss_monitor.log

# 清理旧日志
find /var/log/opengauss_monitor -name "*.log.*" -mtime +30 -delete
```

### 7.3 服务管理

```bash
# 启动服务
systemctl start opengauss-monitor

# 停止服务
systemctl stop opengauss-monitor

# 重启服务
systemctl restart opengauss-monitor

# 查看服务状态
systemctl status opengauss-monitor
```

## 8. 故障排查

### 8.1 常见问题

1. 服务无法启动

   - 检查 Java 环境
   - 检查 Redis 连接
   - 检查配置文件权限
   - 查看系统日志

2. 故障转移失败
   - 检查数据库连接
   - 检查 Docker 权限
   - 检查分布式锁状态
   - 查看详细日志

### 8.2 日志说明

- INFO: 正常操作日志
- WARN: 警告信息
- ERROR: 错误信息

## 9. 注意事项

1. 确保 Redis 服务可靠性
2. 定期检查日志文件大小
3. 及时更新数据库密码
4. 保持系统时间同步
5. 定期备份配置文件

## 10. 联系方式

- 项目维护者：[维护者姓名]
- 邮箱：[联系邮箱]
- 项目地址：[项目 URL]

## 11. 配置界面使用说明

### 11.1 访问配置界面

- URL: http://[服务器 IP]:8080/
- 默认端口: 8080
- 首次访问请使用默认管理员账号登录

### 11.2 配置项说明

1. 系统配置

   - 系统基础参数配置
   - 监控参数配置
   - 告警阈值配置

2. 数据库配置

   - 主备节点信息配置
   - 数据库连接参数配置
   - 备份策略配置

3. 网络配置
   - 网络连接参数配置
   - 防火墙规则配置
   - SSH 连接配置

### 11.3 配置操作指南

1. 初始化配置

   ```
   1. 登录配置界面
   2. 进入"系统初始化"页面
   3. 按向导完成基础配置
   4. 保存并应用配置
   ```

2. 修改配置
   ```
   1. 进入相应配置页面
   2. 修改配置参数
   3. 点击"保存"按钮
   4. 根据提示重启相关服务
   ```

## 12. 脚本管理模块（开发中）

### 12.1 计划功能

- 脚本分类管理
- 脚本在线编辑器
- 脚本执行环境隔离
- 执行权限控制
- 执行日志记录
- 定时任务调度

### 12.2 开发进度

- [x] 基础框架搭建
- [x] 数据库表结构设计
- [ ] 脚本管理接口开发
- [ ] 前端界面开发
- [ ] 执行引擎开发
- [ ] 系统测试

## 13. 常见问题

### 13.1 配置相关

1. 配置保存失败

   - 检查数据库连接
   - 验证配置格式
   - 查看系统日志

2. 配置未生效
   - 确认是否需要重启服务
   - 检查配置文件权限
   - 验证配置值范围

### 13.2 监控相关

1. 监控数据不更新

   - 检查监控进程状态
   - 验证数据库连接
   - 检查网络连通性

2. 告警未触发
   - 确认告警规则配置
   - 检查告警阈值设置
   - 验证通知渠道

## 14. 开发计划

### 14.1 近期计划

1. 完成脚本管理模块开发
2. 优化配置界面交互
3. 增加更多监控指标
4. 提供 API 文档

### 14.2 长期规划

1. 支持多集群管理
2. 引入机器学习预测故障
3. 提供容器化部署方案
4. 支持更多数据库版本

## 15. 联系方式

- 技术支持：[<EMAIL>]
- 问题反馈：[<EMAIL>]
- 项目文档：[http://docs.example.com]

## 16. 版本历史

### v1.0.0 (2024-03-20)

- 初始版本发布
- 基础监控功能
- 故障转移功能

### v1.1.0 (开发中)

- 配置界面优化
- 脚本管理模块
- 更多监控指标

## 9. 登录功能说明

系统提供了基本的登录认证功能，包括以下特性：

### 9.1 默认账号

- 用户名：admin
- 密码：admin
- 角色：ADMIN

### 9.2 API 接口

1. 登录接口

```
POST /api/auth/login
Content-Type: application/json

{
    "username": "admin",
    "password": "admin"
}
```

2. 主机配置接口

```

```

## 统一返回值和异常处理

为了规范 API 接口返回格式，提升开发效率和前后端协作体验，本项目实现了统一的返回值和异常处理机制。

### 最近更新

✅ 项目中的所有控制器已完成统一返回格式的改造，统一使用 Result 对象进行返回。
✅ 全局异常处理器已配置完成，可以自动处理各类异常并转换为统一的返回格式。

### 统一返回值 (Result)

所有 API 接口都应返回统一格式的 `Result` 对象，包含以下字段：

- `code`: 状态码，200 表示成功，其他值表示失败
- `msg`: 返回信息，成功或失败的描述信息
- `data`: 返回数据，可以是任何类型

#### 使用示例

```java
// 返回成功结果
@GetMapping("/users")
public Result<List<User>> listUsers() {
    List<User> users = userService.list();
    return Result.success(users);
}

// 返回成功结果（自定义消息）
@PostMapping("/users")
public Result<User> createUser(@RequestBody User user) {
    userService.save(user);
    return Result.success("用户创建成功", user);
}

// 返回失败结果
@GetMapping("/users/{id}")
public Result<User> getUser(@PathVariable Long id) {
    User user = userService.getById(id);
    if (user == null) {
        return Result.failed("用户不存在");
    }
    return Result.success(user);
}
```

### 自定义业务异常 (BusinessException)

对于业务逻辑异常，应抛出 `BusinessException`，它会被全局异常处理器捕获并转换为统一的返回格式。

#### 使用示例

```java
@Service
public class UserServiceImpl implements UserService {

    @Override
    public User getByUsername(String username) {
        User user = userMapper.selectByUsername(username);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        return user;
    }

    @Override
    public void updatePassword(Long userId, String oldPassword, String newPassword) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED);
        }
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new BusinessException(401, "原密码不正确");
        }
        // 更新密码逻辑
    }
}
```

### 全局异常处理 (GlobalExceptionHandler)

系统已配置全局异常处理器，可以处理以下类型的异常：

1. 业务异常 (`BusinessException`)
2. 参数校验异常 (`MethodArgumentNotValidException`, `BindException`)
3. 其他未知异常 (`Exception`)

所有异常都会被转换为统一的 `Result` 格式返回给前端。

### 状态码定义 (ResultCode)

系统预定义了以下状态码：

- `SUCCESS(200, "操作成功")`
- `FAILED(500, "操作失败")`
- `VALIDATE_FAILED(404, "参数检验失败")`
- `UNAUTHORIZED(401, "暂未登录或token已经过期")`
- `FORBIDDEN(403, "没有相关权限")`

如需添加新的状态码，请在 `ResultCode` 枚举中定义。

## 开发规范

1. 所有 Controller 方法必须返回 `Result` 对象
2. 不要在 Controller 中使用 try-catch 处理业务异常，应该抛出 `BusinessException`
3. 参数校验失败应抛出相应的校验异常，会被自动处理
4. 对于系统异常（如数据库连接失败），可以直接抛出，会被全局异常处理器捕获

遵循以上规范可以保证 API 返回格式的一致性，提高系统的可维护性和用户体验。

## 7. 接口文档

系统集成了 Knife4j 接口文档功能，提供了可视化的 API 接口文档和测试工具。

### 7.1 访问接口文档

启动系统后，访问以下 URL 查看接口文档：

```
http://localhost:9200/doc.html
```

### 7.2 接口文档功能

Knife4j 接口文档提供以下功能：

- 查看所有 API 接口的详细信息
- 在线测试 API 接口
- 下载接口文档（支持 Markdown 和 HTML 格式）
- 查看接口返回数据模型
- 调试接口请求和响应

### 7.3 接口分组

接口按照以下模块进行分组：

- 系统升级管理：提供系统升级相关的接口
- 其他功能模块...

### 7.4 接口文档安全控制

接口文档默认开启，如需关闭或设置访问权限，可在`application.yml`中进行配置：

```yaml
knife4j:
  enable: true # 是否启用Knife4j
  basic:
    enable: true # 是否启用Basic认证
    username: admin # 认证用户名
    password: 123456 # 认证密码
```

升级相关：
网关：
/usr/local/soft/vpn_server_v2/vpn_server 和 /usr/local/soft/vpn_manage_v2/vpn_manage 这两个二进制文件替换就行
客户端：NetGuard-win-1.0.5-x64.exe 随统一门户一起
新客户端文件放到 /data/updater 目录下。 然后把 latest.yaml 这个文件放到 /usr/local/soft/vpn_server_v2
