# VPN 二进制文件升级功能说明

## 功能概述

本次修改为 ICM 系统的升级管理功能增加了对 VPN 相关文件的支持，当升级包中包含相关文件时，系统会自动将它们部署到指定的目标位置。

支持的文件类型包括：

- **VPN 服务端程序** (`vpn_server`)
- **VPN 管理程序** (`vpn_manage`)
- **NetGuard Windows 客户端** (`NetGuard-win-`开头的文件)
- **客户端更新配置** (`latest.yaml`)

## 支持的文件类型

### 1. vpn_server

- **识别条件**: 文件名包含`vpn_server`
- **目标位置**: `/usr/local/soft/vpn_server_v2/vpn_server`
- **操作**: 自动创建目录、复制文件、设置执行权限

### 2. vpn_manage

- **识别条件**: 文件名包含`vpn_manage`
- **目标位置**: `/usr/local/soft/vpn_manage_v2/vpn_manage`
- **操作**: 自动创建目录、复制文件、设置执行权限

### 3. NetGuard Windows 客户端

- **识别条件**: 文件名以`NetGuard-win-`开头
- **目标位置**: `/data/updater/[原文件名]`
- **操作**: 自动创建目录、复制文件（保持原文件名）

### 4. latest.yaml 配置文件

- **识别条件**: 文件名为`latest.yaml`
- **目标位置**: `/usr/local/soft/vpn_server_v2/latest.yaml`
- **操作**: 自动创建目录、复制配置文件

## 功能特性

### 升级功能

- 当升级包中包含 VPN 相关文件时，系统会自动识别并部署到正确位置
- 自动创建目标目录（如果不存在）
- 自动设置文件执行权限（chmod +x）
- 记录详细的操作日志

### 回退功能

- 支持 VPN 文件的版本回退
- 智能查找包含对应文件的历史版本
- 回退过程中同样会设置正确的文件权限
- 支持部分回退（只回退特定类型的文件）

## 技术实现

### 修改的文件

- `src/main/java/com/jykj/icm/service/UpgradeService.java`

### 修改的方法

1. **execUpdate()** - 主升级方法
   - 添加了对`vpn_server`和`vpn_manage`文件的处理逻辑
2. **execUpdateForFile()** - 文件级回退方法
   - 添加了 VPN 文件的回退处理逻辑
3. **execUpdateForIcmFile()** - ICM 文件特殊处理方法
   - 保持原有逻辑不变（仅处理 ICM 相关文件）

### 处理流程

```
升级包解析 → 文件类型识别 → 目标路径确定 → 目录创建 → 文件复制 → 权限设置 → 日志记录
```

## 使用方法

### 升级包准备

1. 将需要升级的文件放入升级包的 ZIP 文件中：
   - `vpn_server` 和/或 `vpn_manage` 二进制文件
   - 以`NetGuard-win-`开头的 Windows 客户端文件
   - `latest.yaml` 配置文件
2. 确保文件名符合命名规范
3. 上传升级包到 ICM 系统

### 执行升级

1. 创建升级任务
2. 选择立即升级或定时升级
3. 系统会自动识别并处理 VPN 文件
4. 查看升级日志确认操作结果

### 版本回退

1. 使用系统的智能回退功能
2. 系统会自动查找包含 VPN 文件的历史版本
3. 执行回退操作时会恢复对应的 VPN 文件版本

## 日志示例

### 升级日志

```
INFO - VPN服务端程序升级完成: /tmp/vpn_server -> /usr/local/soft/vpn_server_v2/vpn_server
INFO - VPN管理程序升级完成: /tmp/vpn_manage -> /usr/local/soft/vpn_manage_v2/vpn_manage
INFO - NetGuard Windows客户端文件升级完成: /tmp/NetGuard-win-v1.0.0.exe -> /data/updater/NetGuard-win-v1.0.0.exe
INFO - latest.yaml配置文件升级完成: /tmp/latest.yaml -> /usr/local/soft/vpn_server_v2/latest.yaml
```

### 回退日志

```
INFO - VPN服务端程序回退完成: /tmp/vpn_server -> /usr/local/soft/vpn_server_v2/vpn_server
INFO - VPN管理程序回退完成: /tmp/vpn_manage -> /usr/local/soft/vpn_manage_v2/vpn_manage
INFO - NetGuard Windows客户端文件回退完成: /tmp/NetGuard-win-v0.9.0.exe -> /data/updater/NetGuard-win-v0.9.0.exe
INFO - latest.yaml配置文件回退完成: /tmp/latest.yaml -> /usr/local/soft/vpn_server_v2/latest.yaml
```

## 注意事项

1. **文件权限**: 系统会自动为 VPN 二进制文件设置执行权限
2. **目录创建**: 如果目标目录不存在，系统会自动创建
3. **文件覆盖**: 升级时会覆盖现有的文件
4. **回退支持**: 支持单独回退特定类型的文件而不影响其他组件
5. **日志记录**: 所有操作都有详细的日志记录，便于排查问题
6. **文件命名**:
   - VPN 二进制文件会被重命名为固定名称
   - NetGuard 客户端文件保持原文件名
   - latest.yaml 文件保持原名称
7. **目录结构**: 确保目标服务器有相应目录的写入权限

## 兼容性

- 与现有的升级系统完全兼容
- 不影响其他类型文件的升级和回退功能
- 支持混合升级包（同时包含 ICM、SSO、VPN、NetGuard 等多种文件）
- 支持灵活的文件组合（可以只升级其中一种或多种文件类型）
