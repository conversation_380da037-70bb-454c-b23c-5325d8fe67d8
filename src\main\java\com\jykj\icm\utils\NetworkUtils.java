/**
 * @Title : IpAddr
 * @Package : com.jykj.icm.utils
 * @Description : 网络相关工具类
 * @Author: 黄杰
 * @Date: 2025-03-24 10:57
 */
package com.jykj.icm.utils;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.NetworkInterface;
import java.net.Socket;
import java.net.SocketException;
import java.net.URL;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.jykj.icm.common.Constants;

import cn.hutool.http.HttpUtil;

/**
 * 网络相关工具类
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2025-03-24
 */
public class NetworkUtils {
    private static final Logger logger = LoggerFactory.getLogger(NetworkUtils.class);

    /**
     * 根据指定的IP地址获取网卡名称
     *
     * @param targetIP 指定的IP地址
     * @return 网卡名称，如果未找到则返回 null
     */
    public static String getNetworkInterfaceNameByIP(String targetIP) {
        try {
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            while (interfaces.hasMoreElements()) {
                NetworkInterface networkInterface = interfaces.nextElement();
                Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress address = addresses.nextElement();
                    if (address.getHostAddress().equals(targetIP)) {
                        return networkInterface.getName();
                    }
                }
            }
        } catch (SocketException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取所有网卡信息（名称、IP地址列表等）
     *
     * @param includeLoopback 是否包含回环接口（如lo）
     * @param includeVirtual 是否包含虚拟网卡（如docker0）
     * @return List<Map>，每个Map结构为： { "name": "ens3", // 网卡名称 "displayName": "ens3", // 网卡显示名称 "isLoopback": false, //
     *         是否回环接口 "isVirtual": false, // 是否虚拟接口 "ips": ["*************", ...] // IP地址列表 }
     */
    public static List<Map<String, Object>> getActiveNetworkInterfaceInfo(boolean includeLoopback,
        boolean includeVirtual) {
        List<Map<String, Object>> result = new ArrayList<>();
        try {
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            while (interfaces.hasMoreElements()) {
                NetworkInterface networkInterface = interfaces.nextElement();
                String name = networkInterface.getName();
                boolean isLoopback = networkInterface.isLoopback();
                boolean isVirtual = name.startsWith("docker") || name.startsWith("veth") || name.startsWith("br-");

                // 根据参数过滤接口
                if ((!includeLoopback && isLoopback) || (!includeVirtual && isVirtual)) {
                    continue;
                }

                // 收集IP地址
                List<String> ips = new ArrayList<>();
                Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress address = addresses.nextElement();
                    ips.add(address.getHostAddress());
                }

                // 构造Map
                if (!ips.isEmpty()) {
                    Map<String, Object> interfaceInfo = new HashMap<>();
                    interfaceInfo.put("name", name);
                    interfaceInfo.put("displayName", networkInterface.getDisplayName());
                    interfaceInfo.put("isLoopback", isLoopback);
                    interfaceInfo.put("isVirtual", isVirtual);
                    interfaceInfo.put("ips", ips);
                    result.add(interfaceInfo);
                }
            }
        } catch (SocketException e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 默认获取所有非回环、非虚拟的网卡信息
     */
    public static List<Map<String, Object>> getActiveNetworkInterfaceInfo() {
        return getActiveNetworkInterfaceInfo(false, false);
    }

    /**
     * 端口检查法（适合网络服务）
     *
     * @param host
     * @param port
     * @param timeout
     * @return
     */
    public static boolean isPortOpen(String host, int port, int timeout) {
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(host, port), timeout);
            return true;
        } catch (IOException e) {
            logger.error("端口检查失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 自定义就绪检查逻辑（如 HTTP 接口探针
     *
     * @param url
     * @param expectedCode
     * @return
     */
    public static boolean checkHttpService(String url, int expectedCode) {
        try {
            HttpURLConnection connection = (HttpURLConnection)new URL(url).openConnection();
            connection.setRequestMethod("GET");
            return connection.getResponseCode() == expectedCode;
        } catch (IOException e) {
            return false;
        }
    }

    public static boolean checkHttpService(String url) {
        try {
            String result = HttpUtil.get(url, Constants.HTTP_TIMEOUT);
            if (result.contains("success")) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            System.out.println("checkHttpService error: " + e.getMessage());
            return false;
        }
    }

    /**
     * 获取IP地址
     *
     * @param request HttpServletRequest
     * @return IP地址
     * <AUTHOR>
     */
    public static String getIpAddr(HttpServletRequest request) {
        String ipAddress = request.getHeader("x-forwarded-for");
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getRemoteAddr();
            if (ipAddress.equals("127.0.0.1") || ipAddress.equals("0:0:0:0:0:0:0:1")) {
                // 根据网卡取本机配置的IP
                InetAddress inet = null;
                try {
                    inet = InetAddress.getLocalHost();
                } catch (UnknownHostException e) {
                    logger.error("获取IP出错", e);
                }
                ipAddress = inet.getHostAddress();
            }
        }
        // 对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
        if (ipAddress != null && ipAddress.length() > 15) { // "***.***.***.***".length() = 15
            if (ipAddress.indexOf(",") > 0) {
                ipAddress = ipAddress.substring(0, ipAddress.indexOf(","));
            }
        }
        return ipAddress;
    }

    public static void main(String[] args) {
        List<Map<String, Object>> activeNetworkInterfaceInfo = getActiveNetworkInterfaceInfo();
        System.out.println(activeNetworkInterfaceInfo);
    }
}