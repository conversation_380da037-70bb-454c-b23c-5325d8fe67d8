package com.jykj.icm.service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jykj.icm.common.exception.BusinessException;
import com.jykj.icm.common.result.Result;
import com.jykj.icm.entity.UpdateData;
import com.jykj.icm.entity.UpgradeLog;
import com.jykj.icm.entity.UpgradePackage;
import com.jykj.icm.entity.UpgradeRequest;
import com.jykj.icm.entity.UpgradeTask;
import com.jykj.icm.utils.ExecuteUtils;
import com.jykj.icm.utils.FileUtils;
import com.jykj.icm.utils.JsonFileUtils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import lombok.SneakyThrows;

/**
 * 升级服务
 */
@Service
public class UpgradeService {
    private static final Logger logger = LoggerFactory.getLogger(UpgradeService.class);

    /**
     * 升级包存储根目录
     */
    @Value("${upgrade.storage.directory:/home/<USER>/upgrade}")
    private String upgradeStorageDirectory;

    /**
     * 存储升级包的JSON文件路径
     */
    @Value("${upgrade.file.packages:${upgrade.storage.directory}/packages.json}")
    private String upgradePackagesFile;

    /**
     * 存储升级任务的JSON文件路径
     */
    @Value("${upgrade.file.tasks:${upgrade.storage.directory}/tasks.json}")
    private String upgradeTasksFile;

    /**
     * 存储上一个版本升级包的JSON文件路径
     */
    @Value("${upgrade.file.previous:${upgrade.storage.directory}/previous.json}")
    private String previousUpgradeFile;

    /**
     * 存储升级历史日志的JSON文件路径
     */
    @Value("${upgrade.file.logs:${upgrade.storage.directory}/upgrade_logs.json}")
    private String upgradeLogsFile;
    /**
     * x-file-storage默认平台
     */
    @Value("${dromara.x-file-storage.default-platform}")
    private String defaultStoragePlatform;

    /**
     * 定时任务执行器
     */
    private ScheduledExecutorService scheduler;

    @Autowired
    private HostConfigServer hostConfigServer;

    @Autowired
    private FileStorageService fileStorageService;

    /**
     * 验证文件存储服务是否可用
     */
    private void validateFileStorageService() {
        if (fileStorageService == null) {
            throw new BusinessException("文件存储服务未配置，请检查x-file-storage配置");
        }
        logger.debug("文件存储服务验证通过");
    }

    /**
     * 初始化方法
     */
    @PostConstruct
    public void init() {
        // 创建存储目录
        File directory = new File(upgradeStorageDirectory);
        if (!directory.exists()) {
            boolean created = directory.mkdirs();
            if (created) {
                logger.info("成功创建升级存储目录: {}", upgradeStorageDirectory);
            } else {
                logger.warn("无法创建升级存储目录: {}", upgradeStorageDirectory);
            }
        }

        logger.info("升级功能初始化，存储目录: {}", upgradeStorageDirectory);
        logger.info("升级包JSON文件: {}", upgradePackagesFile);
        logger.info("升级任务JSON文件: {}", upgradeTasksFile);
        logger.info("上一版本JSON文件: {}", previousUpgradeFile);
        logger.info("升级历史日志JSON文件: {}", upgradeLogsFile);

        // 初始化JSON文件
        initJsonFiles();

        // 初始化定时任务执行器
        scheduler = Executors.newScheduledThreadPool(1);

        // 启动定时任务检查器
        scheduler.scheduleAtFixedRate(this::checkScheduledTasks, 0, 1, TimeUnit.MINUTES);
    }

    /**
     * 初始化JSON文件，确保文件存在
     */
    private void initJsonFiles() {
        try {
            // 初始化升级包JSON文件
            File packagesFile = new File(upgradePackagesFile);
            if (!packagesFile.exists()) {
                Map<String, UpgradePackage> emptyPackages = new HashMap<>();
                JsonFileUtils.saveMapToJson(upgradePackagesFile, emptyPackages);
                logger.info("已初始化升级包JSON文件: {}", upgradePackagesFile);
            }

            // 初始化升级任务JSON文件
            File tasksFile = new File(upgradeTasksFile);
            if (!tasksFile.exists()) {
                Map<String, UpgradeTask> emptyTasks = new HashMap<>();
                JsonFileUtils.saveMapToJson(upgradeTasksFile, emptyTasks);
                logger.info("已初始化升级任务JSON文件: {}", upgradeTasksFile);
            }

            // 初始化升级历史日志JSON文件
            File logsFile = new File(upgradeLogsFile);
            if (!logsFile.exists()) {
                Map<String, UpgradeLog> emptyLogs = new HashMap<>();
                JsonFileUtils.saveMapToJson(upgradeLogsFile, emptyLogs);
                logger.info("已初始化升级历史日志JSON文件: {}", upgradeLogsFile);
            }
        } catch (IOException e) {
            logger.error("初始化JSON文件失败", e);
        }
    }

    /**
     * 销毁方法
     */
    @PreDestroy
    public void destroy() {
        if (scheduler != null) {
            scheduler.shutdown();
        }
    }

    /**
     * 上传升级包
     *
     * @param file    升级包文件
     * @param request 升级请求
     * @return 升级包地址
     */
    public Result<String> uploadUpgradePackage(MultipartFile file, UpgradeRequest request) {
        String id = null;
        File savedFile = null;
        boolean needRollback = false;

        try {
            // 验证请求参数
            if (file == null || file.isEmpty()) {
                throw new BusinessException("升级包文件不能为空");
            }
            if (request.getName() == null || request.getName().isEmpty()) {
                throw new BusinessException("升级包名称不能为空");
            }
            if (!file.getOriginalFilename().toLowerCase().endsWith(".zip")) {
                throw new BusinessException("上传的文件必须是zip格式");
            }
            // 生成唯一ID，当前时间戳
            id = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmssSSS");

            // 创建存储目录
            String packageDir = upgradeStorageDirectory + File.separator + id;

            // 保存文件
            String filePath = FileUtils.saveUploadedFile(file, packageDir);
            return Result.success(filePath);
        } catch (Exception e) {
            logger.error("上传升级包失败", e);
            needRollback = true;
            throw e;
        } finally {
            // 上传失败，删除已保存的文件
            if (needRollback && savedFile != null) {
                savedFile.delete();
            }
        }

    }

    /**
     * 创建升级任务
     *
     * @param packageId 升级包ID
     * @param request   升级请求
     * @return 升级任务ID
     */
    public String createUpgradeTask(String packageId, UpgradeRequest request) {
        // 验证升级包是否存在
        UpgradePackage upgradePackage = getUpgradePackage(packageId);
        if (upgradePackage == null) {
            throw new BusinessException("升级包不存在");
        }

        // 生成唯一ID
        String id = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmssSSS");

        // 创建升级任务
        UpgradeTask task = new UpgradeTask();
        task.setId(id);
        task.setUpgradePackageId(packageId);
        task.setUpgradeType(request.getUpgradeType());
        task.setScheduledTime(request.getScheduledTime());
        task.setStatus(0); // 待执行
        task.setCreateTime(new Date());
        task.setRemark(request.getDescription());

        // 保存到JSON文件
        saveUpgradeTask(task);

        // 如果是立即升级，执行升级
        if (request.getUpgradeType() != null && request.getUpgradeType() == 0) {
            executeUpgrade(id);
        }

        return id;
    }

    /**
     * 执行升级（提示：升级后会重启服务器）
     *
     * @param taskId 升级任务ID
     */
    public void executeUpgrade(String taskId) {
        // 获取升级任务
        UpgradeTask task = getUpgradeTask(taskId);
        if (task == null) {
            throw new BusinessException("升级任务不存在");
        }

        // 更新任务状态为执行中
        task.setStatus(1);
        task.setExecuteTime(new Date());
        saveUpgradeTask(task);
        boolean includeIcmFile = false;
        try {
            // 获取升级包
            UpgradePackage upgradePackage = getUpgradePackage(task.getUpgradePackageId());
            if (upgradePackage == null) {
                throw new BusinessException("升级包不存在");
            }

            // 获取当前版本信息用于回退和日志记录
            UpgradePackage currentPackage = getCurrentUpgradePackage();

            // 保存当前版本为上一个版本，用于版本回退
            if (currentPackage != null) {
                JsonFileUtils.saveObjectToJson(previousUpgradeFile, currentPackage);
            }

            // 创建升级日志
            UpgradeLog upgradeLog = new UpgradeLog();
            String logId = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmssSSS");
            upgradeLog.setId(logId);
            upgradeLog.setFileName(upgradePackage.getName());
            upgradeLog.setTaskId(taskId);
            upgradeLog.setPackageId(upgradePackage.getId());
            upgradeLog.setUpgradeTime(new Date());
            upgradeLog.setOperationType(0); // 设置操作类型为升级
            logger.info("开始升级: {}，文件：{}", upgradePackage.getName(), upgradePackage.getFiles());
            includeIcmFile = upgradePackage.getFiles().keySet().stream().anyMatch(item -> item.contains("icm-"));
            // 设置版本信息
            if (currentPackage != null) {
                upgradeLog.setFromVersion(currentPackage.getVersion());
            } else {
                upgradeLog.setFromVersion("未知版本");
            }
            upgradeLog.setToVersion(upgradePackage.getVersion());
            upgradeLog.setRemark(task.getRemark());

            try {
                // TODO: 执行实际的升级逻辑
                execUpdate(upgradePackage, false);

                // 升级成功，更新日志
                upgradeLog.setResult(0); // 成功
                saveUpgradeLog(upgradeLog);

                logger.info("升级完成: {}", upgradePackage.getName());

                // 更新任务状态为已完成
                task.setStatus(2);
                task.setCompleteTime(new Date());
            } catch (Exception e) {
                logger.error("升级失败", e);

                // 升级失败，更新日志
                upgradeLog.setResult(1); // 失败
                upgradeLog.setErrorMessage(e.getMessage());
                saveUpgradeLog(upgradeLog);

                // 更新任务状态为失败
                task.setStatus(3);
                task.setErrorMessage(e.getMessage());
                task.setCompleteTime(new Date());
                throw e;
            } finally {
                saveUpgradeTask(task);
                // 重启服务器 todo 之后优化
                if (includeIcmFile) {
                    Thread.sleep(10000);
                    ExecuteUtils.executeCommand("sudo", "reboot");
                }
            }
        } catch (Exception e) {
            logger.error("执行升级任务失败", e);
            throw new BusinessException("执行升级任务失败: " + e.getMessage());
        }
    }

    @SneakyThrows
    private void execUpdate(UpgradePackage upgradePackage, boolean needReboot) {
        logger.info("开始执行升级: {}", upgradePackage.getName());

        Boolean forceAssociation = upgradePackage.getForceAssociation();
        if (forceAssociation != null && forceAssociation) {
            // 如果升级包有MinIO文件URL，需要先下载到本地
            String workDir = null;
            if (upgradePackage.getFileUrl() != null) {
                // 创建临时工作目录
                workDir = upgradeStorageDirectory + File.separator + "exec_" + upgradePackage.getId();
                File workDirFile = new File(workDir);
                if (!workDirFile.exists()) {
                    workDirFile.mkdirs();
                }

                // 重新构建FileInfo对象用于下载
                FileInfo fileInfo = new FileInfo();
                // 指定平台，避免 platform:null
                fileInfo.setPlatform(defaultStoragePlatform);
                fileInfo.setUrl(upgradePackage.getFileUrl());
                if (upgradePackage.getObjectName() != null && upgradePackage.getObjectName().contains("/")) {
                    int lastSlashIndex = upgradePackage.getObjectName().lastIndexOf("/");
                    fileInfo.setPath(upgradePackage.getObjectName().substring(0, lastSlashIndex + 1));
                    fileInfo.setFilename(upgradePackage.getObjectName().substring(lastSlashIndex + 1));
                } else {
                    fileInfo.setPath("");
                    fileInfo.setFilename(upgradePackage.getObjectName());
                }
                fileInfo.setSize(upgradePackage.getFileSize());

                // 从MinIO下载并解压文件（用于升级操作）
                downloadAndUnzipFile(fileInfo, workDir);

                // 更新文件路径映射
                Map<String, String> updatedFiles = new HashMap<>();
                String extractedDir = workDir + File.separator + upgradePackage.getName();
                for (Map.Entry<String, String> entry : upgradePackage.getFiles().entrySet()) {
                    String fileName = entry.getKey();
                    String newPath = extractedDir + File.separator + fileName;
                    updatedFiles.put(fileName, newPath);
                }
                upgradePackage.setFiles(updatedFiles);
            }

            // 关联升级 - 先升级服务器，再通知客户端升级
            Map<String, String> files = upgradePackage.getFiles();
            String key;
            String value;

            for (Map.Entry<String, String> entry : files.entrySet()) {
                key = entry.getKey();
                value = entry.getValue();
                if (key.contains("SSO-")) {
                    FileUtil.copy(value, "/home/<USER>/sso/", true);
                    // 统一门户升级 sh /home/<USER>/sso/start.sh sso
                    ExecuteUtils.executeCommand("sh", "/home/<USER>/sso/start.sh", "sso");
                    Thread.sleep(2000);
                    hostConfigServer.updateSso(hostConfigServer.getHostConfig(null), new ArrayList<>());
                    Thread.sleep(3000);
                } else if (key.contains("SSO_client")) {
                    // 通知客户端升级，目前只能统一门户检测，统一门户调用接口、或者调用统一门户的客户端升级接口
                    if (upgradePackage.getForceAssociation() != null && upgradePackage.getForceAssociation()) {
                        UpdateData data = new UpdateData();
                        data.setSsoClientVersion(upgradePackage.getVersion());
                        data.setSsoClientFilePath(value);
                        data.setForceAssociation(upgradePackage.getForceAssociation());
                        String string = HttpUtil.post("https://localhost:9009/common/update/clientUpdate",
                                JSONUtil.toJsonStr(data));
                        logger.info(string);
                    }
                } else if (key.contains("icm-")) {
                    // 把文件拷贝到/home/<USER>/icm/目录下
                    FileUtil.copy(value, "/home/<USER>/icm/", true);
                    // 服务端初始化程序升级

                    // sudo ss -tulnp | grep ':9200' | awk -F'pid=' '{print $2}' | awk -F',' '{print
                    // $1}'
                    // String pid = ExecuteUtils.executeCommandAndReturnPid("sudo ss -tulnp | grep
                    // ':9200'");
                    // if (pid != null && !pid.isEmpty()) {
                    // ExecuteUtils.executeCommand("sudo", "kill", "-9", pid);
                    // }
                    // 先停止服务 sudo kill -9 $(sudo netstat -tulnp | grep ':9200' | awk '{print $7}' |
                    // cut -d '/' -f1)
                    // ExecuteUtils.executeCommand("sudo", "kill", "-9", "$(sudo", "netstat",
                    // "-tulnp", "|", "grep",
                    // "':9200'", "|", "awk", "'{print $7}'", "|", "cut", "-d", "/", "-f1)");
                    // Thread.sleep(1000);
                    // 再启动服务 sh /home/<USER>/icm/icm_auto.sh start
                    // ExecuteUtils.executeCommand("sh", "/home/<USER>/icm/icm_auto.sh", "start");
                    // 异步执行
                    // CompletableFuture.runAsync(() -> {
                    // try {
                    // ExecuteUtils.executeCommand("sh", "/home/<USER>/icm/icm_auto.sh", "restart");
                    // logger.info("升级完成");
                    // } catch (Exception e) {
                    // logger.error("升级失败", e);
                    // }
                    // });

                } else if (key.contains("vpn_server")) {
                    // VPN服务端程序升级
                    String targetDir = "/usr/local/soft/vpn_server_v2/";
                    // 确保目标目录存在
                    File targetDirFile = new File(targetDir);
                    if (!targetDirFile.exists()) {
                        boolean created = targetDirFile.mkdirs();
                        if (created) {
                            logger.info("创建VPN服务端目录: {}", targetDir);
                        }
                    }
                    // 复制文件到目标位置
                    FileUtil.copy(value, targetDir + "vpn_server", true);
                    // 设置执行权限
                    ExecuteUtils.executeCommand("chmod", "+x", targetDir + "vpn_server");
                    logger.info("VPN服务端程序升级完成: {} -> {}", value, targetDir + "vpn_server");
                } else if (key.contains("vpn_manage")) {
                    // VPN管理程序升级
                    String targetDir = "/usr/local/soft/vpn_manage_v2/";
                    // 确保目标目录存在
                    File targetDirFile = new File(targetDir);
                    if (!targetDirFile.exists()) {
                        boolean created = targetDirFile.mkdirs();
                        if (created) {
                            logger.info("创建VPN管理程序目录: {}", targetDir);
                        }
                    }
                    // 复制文件到目标位置
                    FileUtil.copy(value, targetDir + "vpn_manage", true);
                    // 设置执行权限
                    ExecuteUtils.executeCommand("chmod", "+x", targetDir + "vpn_manage");
                    logger.info("VPN管理程序升级完成: {} -> {}", value, targetDir + "vpn_manage");
                } else if (key.startsWith("NetGuard-win-")) {
                    // NetGuard Windows客户端升级
                    String updaterDir = "/data/updater/";
                    // 确保updater目录存在
                    File updaterDirFile = new File(updaterDir);
                    if (!updaterDirFile.exists()) {
                        boolean created = updaterDirFile.mkdirs();
                        if (created) {
                            logger.info("创建NetGuard客户端更新目录: {}", updaterDir);
                        }
                    }
                    // 复制客户端文件到updater目录
                    FileUtil.copy(value, updaterDir + key, true);
                    logger.info("NetGuard Windows客户端文件升级完成: {} -> {}", value, updaterDir + key);
                } else if (key.equals("latest.yaml")) {
                    // latest.yaml配置文件
                    //String vpnServerDir = "/usr/local/soft/vpn_server_v2/";
                    String vpnServerDir = "/data/updater/";
                    // 确保VPN服务端目录存在
                    File vpnServerDirFile = new File(vpnServerDir);
                    if (!vpnServerDirFile.exists()) {
                        boolean created = vpnServerDirFile.mkdirs();
                        if (created) {
                            logger.info("创建VPN服务端目录: {}", vpnServerDir);
                        }
                    }
                    // 复制latest.yaml到VPN服务端目录
                    FileUtil.copy(value, vpnServerDir + "latest.yaml", true);
                    logger.info("latest.yaml配置文件升级完成: {} -> {}", value, vpnServerDir + "latest.yaml");
                }
            }

            // 清理临时工作目录
            if (workDir != null) {
                try {
                    FileUtils.deleteDirectory(new File(workDir));
                    logger.info("清理临时工作目录: {}", workDir);
                } catch (Exception e) {
                    logger.warn("清理临时工作目录失败: {}", workDir, e);
                }
            }

            if (needReboot) {
                // 重启服务器 todo 之后优化
                Thread.sleep(10000);
                ExecuteUtils.executeCommand("sudo", "reboot");
            }
        }
    }

    /**
     * 直接解压上传的文件到指定目录
     *
     * @param file      上传的文件
     * @param targetDir 目标目录
     */
    private void unzipUploadedFile(MultipartFile file, String targetDir) {
        java.nio.file.Path tempFilePath = null;
        try {
            // 创建临时文件
            String tempFileName = "upload_" + DateUtil.format(new Date(), "yyyyMMddHHmmssSSS") + ".zip";
            tempFilePath = java.nio.file.Paths.get(System.getProperty("java.io.tmpdir"), tempFileName);

            logger.info("开始保存上传文件: {} (大小: {} 字节)", file.getOriginalFilename(), file.getSize());

            // 将上传的文件保存到临时位置
            try (java.io.InputStream inputStream = file.getInputStream()) {
                java.nio.file.Files.copy(inputStream, tempFilePath,
                        java.nio.file.StandardCopyOption.REPLACE_EXISTING);
            }

            logger.info("文件保存完成，开始解压到: {}", targetDir);

            // 确保目标目录存在
            File targetDirFile = new File(targetDir);
            if (!targetDirFile.exists()) {
                boolean created = targetDirFile.mkdirs();
                if (!created) {
                    throw new BusinessException("创建目标目录失败: " + targetDir);
                }
            }

            // 解压文件
            FileUtils.unzip(tempFilePath.toFile(), targetDir);

            logger.info("文件解压成功: {} -> {}", file.getOriginalFilename(), targetDir);

        } catch (Exception e) {
            logger.error("文件解压失败: {}", e.getMessage(), e);
            throw new BusinessException("文件解压失败: " + e.getMessage());
        } finally {
            // 确保删除临时文件
            if (tempFilePath != null) {
                try {
                    java.nio.file.Files.deleteIfExists(tempFilePath);
                } catch (Exception e) {
                    logger.warn("删除临时文件失败: {}", tempFilePath, e);
                }
            }
        }
    }

    /**
     * 下载文件并解压到指定目录（用于升级和回退操作）
     *
     * @param fileInfo  文件信息
     * @param targetDir 目标目录
     */
    private void downloadAndUnzipFile(FileInfo fileInfo, String targetDir) {
        Path tempFilePath = null;
        try {
            // 校验文件存储服务
            validateFileStorageService();
            // 兜底设置平台，避免 platform:null
            if (fileInfo.getPlatform() == null || fileInfo.getPlatform().isEmpty()) {
                fileInfo.setPlatform(defaultStoragePlatform);
            }
            // 创建临时文件
            String tempFileName = "temp_" + DateUtil.format(new Date(), "yyyyMMddHHmmssSSS") + ".zip";
            tempFilePath = Paths.get(System.getProperty("java.io.tmpdir"), tempFileName);

            logger.info("开始下载文件: {} (大小: {} 字节)", fileInfo.getUrl(), fileInfo.getSize());

            // 使用x-file-storage下载文件到临时位置
            byte[] fileBytes = fileStorageService.download(fileInfo).bytes();
            Files.write(tempFilePath, fileBytes,
                    StandardOpenOption.CREATE,
                    StandardOpenOption.TRUNCATE_EXISTING);

            logger.info("文件下载完成，开始解压到: {}", targetDir);

            // 确保目标目录存在
            File targetDirFile = new File(targetDir);
            if (!targetDirFile.exists()) {
                boolean created = targetDirFile.mkdirs();
                if (!created) {
                    throw new BusinessException("创建目标目录失败: " + targetDir);
                }
            }

            // 解压文件
            FileUtils.unzip(tempFilePath.toFile(), targetDir);

            logger.info("文件下载并解压成功: {} -> {}", fileInfo.getUrl(), targetDir);

        } catch (Exception e) {
            logger.error("文件下载并解压失败: {}", e.getMessage(), e);
            throw new BusinessException("文件下载并解压失败: " + e.getMessage());
        } finally {
            // 确保删除临时文件
            if (tempFilePath != null) {
                try {
                    Files.deleteIfExists(tempFilePath);
                } catch (Exception e) {
                    logger.warn("删除临时文件失败: {}", tempFilePath, e);
                }
            }
        }
    }

    /**
     * 获取升级包列表
     *
     * @return 升级包列表
     */
    public List<UpgradePackage> getUpgradePackages() {
        try {
            TypeReference<Map<String, UpgradePackage>> typeRef = new TypeReference<Map<String, UpgradePackage>>() {
            };
            Map<String, UpgradePackage> packagesMap = JsonFileUtils.loadMapFromJson(upgradePackagesFile, typeRef);
            return new ArrayList<>(packagesMap.values());
        } catch (IOException e) {
            logger.error("获取升级包列表失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取升级任务列表
     *
     * @return 升级任务列表
     */
    public List<UpgradeTask> getUpgradeTasks() {
        try {
            TypeReference<Map<String, UpgradeTask>> typeRef = new TypeReference<Map<String, UpgradeTask>>() {
            };
            Map<String, UpgradeTask> tasksMap = JsonFileUtils.loadMapFromJson(upgradeTasksFile, typeRef);
            return new ArrayList<>(tasksMap.values());
        } catch (IOException e) {
            logger.error("获取升级任务列表失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取升级包详情
     *
     * @param id 升级包ID
     * @return 升级包详情
     */
    public UpgradePackage getUpgradePackage(String id) {
        try {
            TypeReference<Map<String, UpgradePackage>> typeRef = new TypeReference<Map<String, UpgradePackage>>() {
            };
            Map<String, UpgradePackage> packagesMap = JsonFileUtils.loadMapFromJson(upgradePackagesFile, typeRef);
            return packagesMap.get(id);
        } catch (IOException e) {
            logger.error("获取升级包详情失败", e);
            return null;
        }
    }

    /**
     * 获取升级任务详情
     *
     * @param id 升级任务ID
     * @return 升级任务详情
     */
    public UpgradeTask getUpgradeTask(String id) {
        try {
            TypeReference<Map<String, UpgradeTask>> typeRef = new TypeReference<Map<String, UpgradeTask>>() {
            };
            Map<String, UpgradeTask> tasksMap = JsonFileUtils.loadMapFromJson(upgradeTasksFile, typeRef);
            return tasksMap.get(id);
        } catch (IOException e) {
            logger.error("获取升级任务详情失败", e);
            return null;
        }
    }

    /**
     * 获取当前使用的升级包
     *
     * @return 当前升级包
     */
    public UpgradePackage getCurrentUpgradePackage() {
        // 查找最后一个成功完成的升级任务
        List<UpgradeTask> tasks = getUpgradeTasks();
        UpgradeTask latestTask = null;

        for (UpgradeTask task : tasks) {
            if (task.getStatus() == 2) { // 已完成
                if (latestTask == null || task.getCompleteTime().after(latestTask.getCompleteTime())) {
                    latestTask = task;
                }
            }
        }

        if (latestTask != null) {
            return getUpgradePackage(latestTask.getUpgradePackageId());
        }

        return null;
    }

    /**
     * 获取上一个版本的升级包
     *
     * @return 上一个版本的升级包
     */
    public UpgradePackage getPreviousUpgradePackage() {
        try {
            return JsonFileUtils.loadObjectFromJson(previousUpgradeFile, UpgradePackage.class);
        } catch (IOException e) {
            logger.error("获取上一个版本升级包失败", e);
            return null;
        }
    }

    /**
     * 回退到上一个版本
     *
     * @throws BusinessException 如果回退失败
     */
    public Result<String> rollbackUpgrade() {
        logger.info("开始执行智能版本回退");

        // 获取当前版本
        UpgradePackage currentPackage = getCurrentUpgradePackage();
        if (currentPackage == null) {
            throw new BusinessException("无法获取当前版本信息");
        }

        logger.info("当前版本: {}", currentPackage.getVersion());

        // 检查当前版本是否有文件
        if (currentPackage.getFiles() == null || currentPackage.getFiles().isEmpty()) {
            throw new BusinessException("当前版本不包含任何文件，无法回退");
        }

        // 创建一个回退日志
        UpgradeLog rollbackLog = new UpgradeLog();
        String logId = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmssSSS");
        rollbackLog.setId(logId);
        rollbackLog.setUpgradeTime(new Date());
        rollbackLog.setFromVersion(currentPackage.getVersion());
        rollbackLog.setRemark("智能系统回退");
        rollbackLog.setOperationType(1); // 设置操作类型为回退

        // 用于记录是否有任何文件被成功回退
        boolean anyFileRollbacked = false;
        // 保存失败的文件列表和原因
        List<String> failedFiles = new ArrayList<>();
        StringBuilder toVersions = new StringBuilder();

        // 提取当前版本中的文件类型
        Set<String> fileTypes = new HashSet<>();
        Set<String> icmFileTypes = new HashSet<>();

        for (String fileName : currentPackage.getFiles().keySet()) {
            // 提取文件前缀，如"icm-"、"SSO-"等
            int dashIndex = fileName.indexOf('-');
            if (dashIndex > 0) {
                String fileType = fileName.substring(0, dashIndex + 1);
                // 区分ICM相关文件和其他文件
                if (fileType.startsWith("icm-")) {
                    icmFileTypes.add(fileType);
                } else {
                    fileTypes.add(fileType);
                }
            } else if (fileName.contains("SSO_client")) {
                fileTypes.add("SSO_client");
            }
        }

        logger.info("当前版本包含文件类型: 普通文件={}, ICM文件={}", fileTypes, icmFileTypes);

        try {
            // 先处理非ICM文件类型
            processFileTypes(fileTypes, failedFiles, toVersions);

            // 检查是否有任何非ICM文件已经回退成功
            if (toVersions.length() > 0) {
                anyFileRollbacked = true;
            }

            // 最后处理ICM文件类型，因为这些文件可能影响程序本身的运行
            if (!icmFileTypes.isEmpty()) {
                logger.info("开始处理ICM文件的回退，这些文件将在最后进行回退");
                processFileTypes(icmFileTypes, failedFiles, toVersions);

                // 再次检查是否有任何文件回退成功
                if (toVersions.length() > 0) {
                    anyFileRollbacked = true;
                }
            }

            if (!anyFileRollbacked) {
                throw new BusinessException("所有文件回退失败: " + String.join("; ", failedFiles));
            }

            // 更新回退日志
            rollbackLog.setToVersion(toVersions.toString());
            rollbackLog.setResult(failedFiles.isEmpty() ? 0 : 2); // 0-全部成功, 2-部分成功
            rollbackLog.setFileName(currentPackage.getName() + " (智能回退)");
            if (!failedFiles.isEmpty()) {
                rollbackLog.setErrorMessage("部分文件回退失败: " + String.join("; ", failedFiles));
            }
            saveUpgradeLog(rollbackLog);

            // 保存当前版本为新的上一个版本，以便可以再次回退
            JsonFileUtils.saveObjectToJson(previousUpgradeFile, currentPackage);

            logger.info("智能版本回退完成，结果: {}", failedFiles.isEmpty() ? "全部成功" : "部分成功");

            if (!icmFileTypes.isEmpty()) {
                // 重启服务器
                Thread.sleep(10000);
                ExecuteUtils.executeCommand("sudo", "reboot");
                return Result.success("回退成功，系统会自动重启，请10-20秒后刷新界面");
            }
            return Result.success("回退成功，请刷新界面");
        } catch (Exception e) {
            logger.error("执行智能版本回退失败", e);

            // 更新回退日志
            rollbackLog.setResult(1); // 1-失败
            rollbackLog.setErrorMessage(e.getMessage());
            saveUpgradeLog(rollbackLog);
            throw new BusinessException("执行智能版本回退失败: " + e.getMessage());
        }
    }

    /**
     * 处理特定类型的文件回退
     *
     * @param fileTypes   文件类型集合
     * @param failedFiles 失败文件列表(会被更新)
     * @param toVersions  目标版本信息(会被更新)
     * @throws Exception 处理过程中的异常
     */
    private void processFileTypes(Set<String> fileTypes, List<String> failedFiles, StringBuilder toVersions)
            throws Exception {
        boolean isIcmProcessing = false;

        // 遍历每种文件类型，分别查找并回退
        for (String fileType : fileTypes) {
            // 检查是否处理ICM相关文件
            isIcmProcessing = fileType.startsWith("icm-");

            try {
                // 查找包含该类型文件的上一个版本包
                UpgradePackage previousPackage = findPreviousPackageWithFile(fileType);
                if (previousPackage == null) {
                    logger.warn("未找到包含文件类型 {} 的上一个版本", fileType);
                    failedFiles.add(fileType + "*");
                    continue;
                }

                logger.info("文件类型 {} 将回退到版本 {}", fileType, previousPackage.getVersion());

                // 执行实际回退操作，只回退指定类型的文件
                // 如果是ICM文件，使用特殊的处理逻辑
                if (isIcmProcessing) {
                    execUpdateForIcmFile(previousPackage, fileType);
                } else {
                    execUpdateForFile(previousPackage, fileType);
                }

                if (toVersions.length() > 0) {
                    toVersions.append(", ");
                }
                toVersions.append(previousPackage.getVersion()).append("(").append(fileType).append("*)");

            } catch (Exception e) {
                logger.error("回退文件类型 {} 失败: {}", fileType, e.getMessage());
                failedFiles.add(fileType + "*: " + e.getMessage());
            }
        }
    }

    /**
     * 特殊处理ICM类型文件的回退，因为ICM是执行回退的程序本身
     *
     * @param upgradePackage 升级包
     * @param targetFileName 目标文件名
     * @throws Exception 如果操作失败
     */
    @SneakyThrows
    private void execUpdateForIcmFile(UpgradePackage upgradePackage, String targetFileName) {
        logger.info("开始执行ICM文件回退: {} 到 {}", targetFileName, upgradePackage.getName());

        // 首先创建临时目录
        String tempDir = upgradeStorageDirectory + File.separator + "temp_rollback_"
                + DateUtil.format(DateUtil.date(), "yyyyMMddHHmmssSSS");
        File tempDirFile = new File(tempDir);
        tempDirFile.mkdirs();

        try {
            // 如果升级包有MinIO文件URL，需要先下载到本地
            if (upgradePackage.getFileUrl() != null) {
                // 重新构建FileInfo对象用于下载
                FileInfo fileInfo = new FileInfo();
                // 指定平台，避免 platform:null
                fileInfo.setPlatform(defaultStoragePlatform);
                fileInfo.setUrl(upgradePackage.getFileUrl());
                fileInfo.setPath(
                        upgradePackage.getObjectName().substring(0, upgradePackage.getObjectName().lastIndexOf("/")));
                fileInfo.setFilename(
                        upgradePackage.getObjectName().substring(upgradePackage.getObjectName().lastIndexOf("/") + 1));

                // 从MinIO下载并解压文件（用于回退操作）
                downloadAndUnzipFile(fileInfo, tempDir);

                // 更新文件路径映射
                Map<String, String> updatedFiles = new HashMap<>();
                String extractedDir = tempDir + File.separator + upgradePackage.getName();
                for (Map.Entry<String, String> entry : upgradePackage.getFiles().entrySet()) {
                    String fileName = entry.getKey();
                    String newPath = extractedDir + File.separator + fileName;
                    updatedFiles.put(fileName, newPath);
                }
                upgradePackage.setFiles(updatedFiles);
            }

            // 关联升级，但仅处理指定的文件
            Map<String, String> files = upgradePackage.getFiles();
            String key;
            String value;

            for (Map.Entry<String, String> entry : files.entrySet()) {
                key = entry.getKey();
                value = entry.getValue();

                // 仅处理目标文件
                if (key.contains(targetFileName)) {
                    // 把文件拷贝到/home/<USER>/icm/目录下，等待重启后生效
                    FileUtil.copy(value, "/home/<USER>/icm/", true);
                }
            }
        } catch (Exception e) {
            logger.error("准备ICM文件回退失败", e);
            throw e;
        } finally {
            // 清理临时目录
            try {
                FileUtils.deleteDirectory(tempDirFile);
                logger.info("清理ICM回退临时目录: {}", tempDir);
            } catch (Exception e) {
                logger.warn("清理ICM回退临时目录失败: {}", tempDir, e);
            }
        }
    }

    /**
     * 查找包含指定文件的最近一个升级包
     *
     * @param fileName 文件名
     * @return 包含该文件的升级包，如果没有找到则返回null
     */
    private UpgradePackage findPreviousPackageWithFile(String fileName) {
        // 获取所有历史升级任务，按时间倒序排列
        List<UpgradeTask> tasks = getUpgradeTasks();
        tasks.sort((a, b) -> b.getCompleteTime().compareTo(a.getCompleteTime()));

        // 获取当前版本
        UpgradePackage currentPackage = getCurrentUpgradePackage();

        // 遍历所有成功完成的任务，查找包含指定文件的最近一个升级包
        for (UpgradeTask task : tasks) {
            // 仅考虑已完成的任务
            if (task.getStatus() == 2) {
                // 跳过当前包
                if (currentPackage != null && task.getUpgradePackageId().equals(currentPackage.getId())) {
                    continue;
                }

                // 获取升级包
                UpgradePackage pkg = getUpgradePackage(task.getUpgradePackageId());
                if (pkg != null && pkg.getFiles() != null) {
                    // 检查是否包含指定文件
                    for (String key : pkg.getFiles().keySet()) {
                        if (key.contains(fileName)) {
                            return pkg;
                        }
                    }
                }
            }
        }

        return null;
    }

    /**
     * 仅针对特定文件执行升级操作
     *
     * @param upgradePackage 升级包
     * @param targetFileName 目标文件名
     * @throws Exception 如果操作失败
     */
    @SneakyThrows
    private void execUpdateForFile(UpgradePackage upgradePackage, String targetFileName) {
        logger.info("开始执行文件回退: {} 到 {}", targetFileName, upgradePackage.getName());

        Boolean forceAssociation = upgradePackage.getForceAssociation();

        if (forceAssociation != null && forceAssociation) {
            // 如果升级包有MinIO文件URL，需要先下载到本地
            String workDir = null;
            if (upgradePackage.getFileUrl() != null) {
                // 创建临时工作目录
                workDir = upgradeStorageDirectory + File.separator + "rollback_" + upgradePackage.getId() + "_"
                        + DateUtil.format(new Date(), "yyyyMMddHHmmssSSS");
                File workDirFile = new File(workDir);
                if (!workDirFile.exists()) {
                    workDirFile.mkdirs();
                }

                // 重新构建FileInfo对象用于下载
                FileInfo fileInfo = new FileInfo();
                // 指定平台，避免 platform:null
                fileInfo.setPlatform(defaultStoragePlatform);
                fileInfo.setUrl(upgradePackage.getFileUrl());
                fileInfo.setPath(
                        upgradePackage.getObjectName().substring(0, upgradePackage.getObjectName().lastIndexOf("/")));
                fileInfo.setFilename(
                        upgradePackage.getObjectName().substring(upgradePackage.getObjectName().lastIndexOf("/") + 1));

                // 从MinIO下载并解压文件（用于ICM文件回退）
                downloadAndUnzipFile(fileInfo, workDir);

                // 更新文件路径映射
                Map<String, String> updatedFiles = new HashMap<>();
                String extractedDir = workDir + File.separator + upgradePackage.getName();
                for (Map.Entry<String, String> entry : upgradePackage.getFiles().entrySet()) {
                    String fileName = entry.getKey();
                    String newPath = extractedDir + File.separator + fileName;
                    updatedFiles.put(fileName, newPath);
                }
                upgradePackage.setFiles(updatedFiles);
            }

            // 关联升级，但仅处理指定的文件
            Map<String, String> files = upgradePackage.getFiles();
            String key;
            String value;

            try {
                for (Map.Entry<String, String> entry : files.entrySet()) {
                    key = entry.getKey();
                    value = entry.getValue();

                    // 仅处理目标文件
                    if (key.contains(targetFileName)) {
                        if (key.contains("SSO-")) {
                            FileUtil.copy(value, "/home/<USER>/sso/", true);
                            // 统一门户升级
                            ExecuteUtils.executeCommand("sh", "/home/<USER>/sso/start.sh", "sso");
                            Thread.sleep(2000);
                            hostConfigServer.updateSso(hostConfigServer.getHostConfig(null), new ArrayList<>());
                            Thread.sleep(3000);
                        } else if (key.contains("SSO_client")) {
                            // 通知客户端升级
                            if (upgradePackage.getForceAssociation() != null && upgradePackage.getForceAssociation()) {
                                UpdateData data = new UpdateData();
                                data.setSsoClientVersion(upgradePackage.getVersion());
                                data.setSsoClientFilePath(value);
                                data.setForceAssociation(upgradePackage.getForceAssociation());
                                String string = HttpUtil.post("https://localhost:9009/common/update/clientUpdate",
                                        JSONUtil.toJsonStr(data));
                                logger.info(string);
                            }
                        } else if (key.contains("icm-")) {
                            // 复制文件到指定目录
                            FileUtil.copy(value, "/home/<USER>/icm/", true);
                            // 服务端初始化程序升级
                        } else if (key.contains("vpn_server")) {
                            // VPN服务端程序回退
                            String targetDir = "/usr/local/soft/vpn_server_v2/";
                            // 确保目标目录存在
                            File targetDirFile = new File(targetDir);
                            if (!targetDirFile.exists()) {
                                boolean created = targetDirFile.mkdirs();
                                if (created) {
                                    logger.info("创建VPN服务端目录: {}", targetDir);
                                }
                            }
                            // 复制文件到目标位置
                            FileUtil.copy(value, targetDir + "vpn_server", true);
                            // 设置执行权限
                            ExecuteUtils.executeCommand("chmod", "+x", targetDir + "vpn_server");
                            logger.info("VPN服务端程序回退完成: {} -> {}", value, targetDir + "vpn_server");
                        } else if (key.contains("vpn_manage")) {
                            // VPN管理程序回退
                            String targetDir = "/usr/local/soft/vpn_manage_v2/";
                            // 确保目标目录存在
                            File targetDirFile = new File(targetDir);
                            if (!targetDirFile.exists()) {
                                boolean created = targetDirFile.mkdirs();
                                if (created) {
                                    logger.info("创建VPN管理程序目录: {}", targetDir);
                                }
                            }
                            // 复制文件到目标位置
                            FileUtil.copy(value, targetDir + "vpn_manage", true);
                            // 设置执行权限
                            ExecuteUtils.executeCommand("chmod", "+x", targetDir + "vpn_manage");
                            logger.info("VPN管理程序回退完成: {} -> {}", value, targetDir + "vpn_manage");
                        } else if (key.startsWith("NetGuard-win-")) {
                            // NetGuard Windows客户端回退
                            String updaterDir = "/data/updater/";
                            // 确保updater目录存在
                            File updaterDirFile = new File(updaterDir);
                            if (!updaterDirFile.exists()) {
                                boolean created = updaterDirFile.mkdirs();
                                if (created) {
                                    logger.info("创建NetGuard客户端更新目录: {}", updaterDir);
                                }
                            }
                            // 复制客户端文件到updater目录
                            FileUtil.copy(value, updaterDir + key, true);
                            logger.info("NetGuard Windows客户端文件回退完成: {} -> {}", value, updaterDir + key);
                        } else if (key.equals("latest.yaml")) {
                            // latest.yaml配置文件回退
                            // String vpnServerDir = "/usr/local/soft/vpn_server_v2/";
                            String vpnServerDir = "/data/updater/";
                            // 确保VPN服务端目录存在
                            File vpnServerDirFile = new File(vpnServerDir);
                            if (!vpnServerDirFile.exists()) {
                                boolean created = vpnServerDirFile.mkdirs();
                                if (created) {
                                    logger.info("创建VPN服务端目录: {}", vpnServerDir);
                                }
                            }
                            // 复制latest.yaml到VPN服务端目录
                            FileUtil.copy(value, vpnServerDir + "latest.yaml", true);
                            logger.info("latest.yaml配置文件回退完成: {} -> {}", value, vpnServerDir + "latest.yaml");
                        }
                    }
                }
            } finally {
                // 清理临时工作目录
                if (workDir != null) {
                    try {
                        FileUtils.deleteDirectory(new File(workDir));
                        logger.info("清理临时工作目录: {}", workDir);
                    } catch (Exception e) {
                        logger.warn("清理临时工作目录失败: {}", workDir, e);
                    }
                }
            }
        }
    }

    /**
     * 保存升级包
     *
     * @param upgradePackage 升级包
     */
    private void saveUpgradePackage(UpgradePackage upgradePackage) {
        try {
            // 加载现有的升级包Map
            TypeReference<Map<String, UpgradePackage>> typeRef = new TypeReference<Map<String, UpgradePackage>>() {
            };
            Map<String, UpgradePackage> packagesMap = JsonFileUtils.loadMapFromJson(upgradePackagesFile, typeRef);

            // 添加或更新升级包
            packagesMap.put(upgradePackage.getId(), upgradePackage);

            // 保存回文件
            JsonFileUtils.saveMapToJson(upgradePackagesFile, packagesMap);
        } catch (IOException e) {
            logger.error("保存升级包失败", e);
            throw new RuntimeException("保存升级包失败", e);
        }
    }

    /**
     * 保存升级任务
     *
     * @param task 升级任务
     */
    private void saveUpgradeTask(UpgradeTask task) {
        try {
            // 加载现有的任务Map
            TypeReference<Map<String, UpgradeTask>> typeRef = new TypeReference<Map<String, UpgradeTask>>() {
            };
            Map<String, UpgradeTask> tasksMap = JsonFileUtils.loadMapFromJson(upgradeTasksFile, typeRef);

            // 添加或更新任务
            tasksMap.put(task.getId(), task);

            // 保存回文件
            JsonFileUtils.saveMapToJson(upgradeTasksFile, tasksMap);
        } catch (IOException e) {
            logger.error("保存升级任务失败", e);
            throw new RuntimeException("保存升级任务失败", e);
        }
    }

    /**
     * 检查定时升级任务
     */
    private void checkScheduledTasks() {
        try {
            List<UpgradeTask> tasks = getUpgradeTasks();
            Date now = new Date();

            for (UpgradeTask task : tasks) {
                // 只处理待执行的定时升级任务
                if (task.getStatus() == 0 && task.getUpgradeType() == 1 && task.getScheduledTime() != null) {
                    // 如果已到预定时间，执行升级
                    if (task.getScheduledTime().before(now)) {
                        executeUpgrade(task.getId());
                    }
                }
            }
        } catch (Exception e) {
            logger.error("检查定时升级任务失败", e);
        }
    }

    /**
     * 取消定时升级任务
     *
     * @param taskId 任务ID
     */
    public void cancelScheduledTask(String taskId) {
        // 获取升级任务
        UpgradeTask task = getUpgradeTask(taskId);
        if (task == null) {
            throw new BusinessException("升级任务不存在");
        }

        // 只能取消待执行的定时升级任务
        if (task.getStatus() != 0 || task.getUpgradeType() != 1) {
            throw new BusinessException("只能取消待执行的定时升级任务");
        }

        // 更新任务状态为已取消
        task.setStatus(4); // 4-已取消
        task.setCompleteTime(new Date());
        task.setRemark(task.getRemark() + " (已取消)");

        // 保存任务状态
        saveUpgradeTask(task);
        logger.info("已取消定时升级任务: {}", taskId);
    }

    /**
     * 删除升级包
     *
     * @param packageId 升级包ID
     */
    public void deleteUpgradePackage(String packageId) {
        // 获取升级包
        UpgradePackage upgradePackage = getUpgradePackage(packageId);
        if (upgradePackage == null) {
            throw new BusinessException("升级包不存在");
        }

        // 检查是否有正在执行的升级任务
        List<UpgradeTask> tasks = getUpgradeTasks();
        for (UpgradeTask task : tasks) {
            if (task.getUpgradePackageId().equals(packageId)) {
                if (task.getStatus() == 1) { // 执行中
                    throw new BusinessException("该升级包有正在执行的升级任务，无法删除");
                }
            }
        }

        // 删除升级包文件
        // 1. 删除MinIO中的文件
        if (upgradePackage.getFileUrl() != null) {
            try {
                FileInfo fileInfo = new FileInfo();
                // 指定平台，避免 platform:null
                fileInfo.setPlatform(defaultStoragePlatform);
                fileInfo.setUrl(upgradePackage.getFileUrl());
                if (upgradePackage.getObjectName() != null && upgradePackage.getObjectName().contains("/")) {
                    int lastSlashIndex = upgradePackage.getObjectName().lastIndexOf("/");
                    fileInfo.setPath(upgradePackage.getObjectName().substring(0, lastSlashIndex + 1));
                    fileInfo.setFilename(upgradePackage.getObjectName().substring(lastSlashIndex + 1));
                } else if (upgradePackage.getObjectName() != null) {
                    fileInfo.setPath("");
                    fileInfo.setFilename(upgradePackage.getObjectName());
                }
                fileInfo.setSize(upgradePackage.getFileSize());

                // 使用x-file-storage删除文件
                fileStorageService.delete(fileInfo);
                logger.info("删除MinIO文件成功: {}", upgradePackage.getFileUrl());
            } catch (Exception e) {
                logger.error("删除MinIO文件失败: {}", upgradePackage.getFileUrl(), e);
                throw new BusinessException("删除MinIO文件失败: " + e.getMessage());
            }
        }

        // 2. 删除本地临时目录（如果存在）
        if (upgradePackage.getStoragePath() != null) {
            File packageDir = new File(upgradePackage.getStoragePath());
            if (packageDir.exists()) {
                try {
                    FileUtils.deleteDirectory(packageDir);
                    logger.info("删除本地临时目录成功: {}", packageDir.getAbsolutePath());
                } catch (Exception e) {
                    logger.warn("删除本地临时目录失败: {}", packageDir.getAbsolutePath(), e);
                    // 本地临时目录删除失败不影响整体操作
                }
            }
        }

        // 从JSON文件中移除升级包
        try {
            TypeReference<Map<String, UpgradePackage>> typeRef = new TypeReference<Map<String, UpgradePackage>>() {
            };
            Map<String, UpgradePackage> packagesMap = JsonFileUtils.loadMapFromJson(upgradePackagesFile, typeRef);
            packagesMap.remove(packageId);
            JsonFileUtils.saveMapToJson(upgradePackagesFile, packagesMap);
        } catch (IOException e) {
            logger.error("从JSON文件中移除升级包失败", e);
            throw new BusinessException("从JSON文件中移除升级包失败: " + e.getMessage());
        }

        // 处理相关的定时任务
        for (UpgradeTask task : tasks) {
            if (task.getUpgradePackageId().equals(packageId)) {
                if (task.getStatus() == 0) { // 只删除待执行
                    // 更新任务状态为已取消
                    task.setStatus(4); // 4-已取消
                    task.setCompleteTime(new Date());
                    task.setRemark(task.getRemark() + " (升级包已删除)");
                    saveUpgradeTask(task);
                }
            }
        }

        logger.info("已删除升级包: {}", packageId);
    }

    public boolean md5Check(String filePath, String md5) {
        boolean needRollback = false;
        File directory = null;
        try {
            File savedFile = new File(filePath);
            // 创建存储目录
            // 生成唯一ID，当前时间戳
            String id = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmssSSS");
            String packageDir = upgradeStorageDirectory + File.separator + id;
            directory = new File(packageDir);
            // 验证MD5值
            if (md5 != null && !md5.isEmpty()) {

                boolean isValid = FileUtils.verifyMD5ByHutoolsAndCodec(savedFile, md5);
                if (!isValid) {
                    throw new BusinessException("文件MD5校验失败");
                }
            }

            // 解压文件
            FileUtils.unzip(savedFile, packageDir);
            needRollback = true;
            UpgradeRequest request = new UpgradeRequest();
            request.setName(savedFile.getName().replace(".zip", ""));
            request.setMd5(md5);
            request.setUpgradeType(0);
            request.setScheduledTime(null);
            // 读取配置文件update.conf
            String updateConfPath = packageDir + File.separator + request.getName() + File.separator + "update.conf";
            File updateConfFile = new File(updateConfPath);
            if (!updateConfFile.exists()) {
                throw new BusinessException("升级包缺少update.conf配置文件");
            }

            String updateConf = FileUtil.readUtf8String(updateConfPath);
            String[] lines = updateConf.split("\n");
            String version = "";
            Boolean forceAssociation = false;
            String description = "";
            Map<String, String> files = new HashMap<>();
            String fullPath = packageDir + File.separator + request.getName() + File.separator;
            for (String line : lines) {
                if (line.startsWith("version:")) {
                    version = line.split(":")[1].trim();
                } else if (line.startsWith("forceAssociation:")) {
                    forceAssociation = Boolean.parseBoolean(line.split(":")[1].trim());
                } else if (line.startsWith("description:")) {
                    description = line.split(":")[1].trim();
                } else if (line.startsWith("files:")) {
                    String[] fileNames = line.split(":")[1].trim().split(",");
                    for (String fileName : fileNames) {
                        files.put(fileName.trim(), fullPath + fileName.trim());
                    }
                }
            }

            if (version.isEmpty()) {
                throw new BusinessException("update.conf中缺少version配置");
            }

            request.setVersion(version);
            request.setForceAssociation(forceAssociation);
            request.setDescription(description);
            request.setFiles(files);

            // 创建升级包对象
            UpgradePackage upgradePackage = new UpgradePackage();
            upgradePackage.setId(id);
            upgradePackage.setName(request.getName());
            upgradePackage.setVersion(request.getVersion());
            upgradePackage.setMd5(request.getMd5());
            upgradePackage
                    .setForceAssociation(request.getForceAssociation() != null ? request.getForceAssociation() : false);
            upgradePackage.setUploadTime(new Date());
            upgradePackage.setDescription(request.getDescription());
            upgradePackage.setStoragePath(packageDir);

            // 设置文件列表
            if (request.getFiles() != null) {
                upgradePackage.setFiles(request.getFiles());
            }

            // 保存到JSON文件
            saveUpgradePackage(upgradePackage);
            needRollback = false;

            // 如果是立即升级，创建升级任务
            if (request.getUpgradeType() != null) {
                createUpgradeTask(id, request);
            }
            return true;
        } catch (Exception e) {
            logger.error("上传升级包失败", e);
            // 如果需要回退，删除已创建的文件和目录
            if (needRollback && directory != null && directory.exists()) {
                try {
                    FileUtils.deleteDirectory(directory);
                } catch (Exception ex) {
                    logger.error("回退删除目录失败: {}", directory.getAbsolutePath(), ex);
                }
            }
            throw new BusinessException("上传升级包失败: " + e.getMessage());
        }
    }

    /**
     * 上传升级包和Md5校验
     *
     * @param file    升级包文件
     * @param request 升级请求
     * @return 升级包ID
     */
    public UpgradeRequest uploadUpgradePackageAndCheckMd5(MultipartFile file, UpgradeRequest request) {
        String id = null;
        FileInfo fileInfo = null;
        String tempDir = null;
        boolean needRollback = false;

        try {
            // 验证文件存储服务
            validateFileStorageService();

            // 验证请求参数
            if (file == null || file.isEmpty()) {
                throw new BusinessException("升级包文件不能为空");
            }
            if (request.getName() == null || request.getName().isEmpty()) {
                throw new BusinessException("升级包名称不能为空");
            }
            if (file.getOriginalFilename() == null || !file.getOriginalFilename().toLowerCase().endsWith(".zip")) {
                throw new BusinessException("上传的文件必须是zip格式");
            }

            // // 验证文件大小（限制为100MB）
            // long maxFileSize = 100 * 1024 * 1024; // 100MB
            // if (file.getSize() > maxFileSize) {
            // throw new BusinessException("文件大小不能超过100MB，当前文件大小: " + (file.getSize() / 1024
            // / 1024) + "MB");
            // }

            logger.info("开始上传升级包: {} (大小: {} 字节)", file.getOriginalFilename(), file.getSize());

            // 生成唯一ID，当前时间戳
            id = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmssSSS");

            // 使用x-file-storage上传文件到MinIO
            fileInfo = fileStorageService.of(file)
                    .setPath("upgrade-packages/" + id + "/")
                    .setObjectId(id)
                    .setSaveFilename(file.getOriginalFilename())
                    .putAttr("type", "upgrade-package")
                    .upload();
            needRollback = true;

            // 验证MD5值（可选）
            if (request.getMd5() != null && !request.getMd5().isEmpty()) {
                logger.info("开始MD5校验，文件大小: {} 字节", fileInfo.getSize());
                try {
                    // 下载文件并计算MD5进行验证
                    byte[] fileBytes = fileStorageService.download(fileInfo).bytes();
                    String actualMd5 = org.apache.commons.codec.digest.DigestUtils.md5Hex(fileBytes);
                    if (!request.getMd5().equalsIgnoreCase(actualMd5)) {
                        logger.error("MD5校验失败: 期望={}, 实际={}", request.getMd5(), actualMd5);
                        throw new BusinessException("文件MD5校验失败，期望: " + request.getMd5() + "，实际: " + actualMd5);
                    }
                    logger.info("MD5校验通过: {}", actualMd5);
                } catch (Exception e) {
                    if (e instanceof BusinessException) {
                        throw e;
                    }
                    logger.error("MD5校验过程中发生错误", e);
                    throw new BusinessException("MD5校验失败: " + e.getMessage());
                }
            } else {
                logger.info("跳过MD5校验（未提供MD5值）");
            }

            // 创建临时目录用于解压
            tempDir = upgradeStorageDirectory + File.separator + "temp_" + id + "_" + System.currentTimeMillis();
            File tempDirFile = new File(tempDir);
            if (!tempDirFile.exists()) {
                boolean created = tempDirFile.mkdirs();
                if (!created) {
                    throw new BusinessException("创建临时目录失败: " + tempDir);
                }
            }

            // 直接解压原始上传文件到临时目录
            // 注意：这里使用原始上传文件，避免不必要的下载操作
            logger.info("开始解压上传文件到临时目录: {}", tempDir);
            unzipUploadedFile(file, tempDir);
            needRollback = true;

            // 读取配置文件update.conf
            String updateConfPath = tempDir + File.separator + request.getName() + File.separator + "update.conf";
            File updateConfFile = new File(updateConfPath);
            if (!updateConfFile.exists()) {
                throw new BusinessException("升级包缺少update.conf配置文件");
            }

            String updateConf = FileUtil.readUtf8String(updateConfPath);
            String[] lines = updateConf.split("\n");
            String version = "";
            Boolean forceAssociation = false;
            String description = "";
            Map<String, String> files = new HashMap<>();
            String fullPath = tempDir + File.separator + request.getName() + File.separator;
            for (String line : lines) {
                if (line.startsWith("version:")) {
                    version = line.split(":")[1].trim();
                } else if (line.startsWith("forceAssociation:")) {
                    forceAssociation = Boolean.parseBoolean(line.split(":")[1].trim());
                } else if (line.startsWith("description:")) {
                    description = line.split(":")[1].trim();
                } else if (line.startsWith("files:")) {
                    String[] fileNames = line.split(":")[1].trim().split(",");
                    for (String fileName : fileNames) {
                        files.put(fileName.trim(), fullPath + fileName.trim());
                    }
                }
            }

            if (version.isEmpty()) {
                throw new BusinessException("update.conf中缺少version配置");
            }

            request.setVersion(version);
            request.setForceAssociation(forceAssociation);
            request.setDescription(description);
            request.setFiles(files);

            // 创建升级包对象
            UpgradePackage upgradePackage = new UpgradePackage();
            upgradePackage.setId(id);
            upgradePackage.setName(request.getName());
            upgradePackage.setVersion(request.getVersion());
            upgradePackage.setMd5(request.getMd5());
            upgradePackage
                    .setForceAssociation(request.getForceAssociation() != null ? request.getForceAssociation() : false);
            upgradePackage.setUploadTime(new Date());
            upgradePackage.setDescription(request.getDescription());

            // 设置MinIO相关信息
            upgradePackage.setFileUrl(fileInfo.getUrl());
            upgradePackage.setObjectName(fileInfo.getPath() + fileInfo.getFilename());
            upgradePackage.setFileSize(fileInfo.getSize());

            // 添加调试日志
            logger.info("文件上传完成:");
            logger.info("  URL: {}", fileInfo.getUrl());
            logger.info("  Path: {}", fileInfo.getPath());
            logger.info("  Filename: {}", fileInfo.getFilename());
            logger.info("  ObjectName: {}", upgradePackage.getObjectName());

            // 兼容旧版本，保留本地路径（临时目录）
            upgradePackage.setStoragePath(tempDir);

            // 设置文件列表
            if (request.getFiles() != null) {
                upgradePackage.setFiles(request.getFiles());
            }

            // 保存到JSON文件
            saveUpgradePackage(upgradePackage);
            needRollback = false;

            // 设置返回的MinIO相关信息
            request.setFileUrl(fileInfo.getUrl());
            request.setObjectName(fileInfo.getPath() + fileInfo.getFilename());
            request.setFileSize(fileInfo.getSize());

            // 如果是立即升级，创建升级任务
            if (request.getUpgradeType() != null) {
                createUpgradeTask(id, request);
            }
            request.setPackageId(id);
            return request;
        } catch (Exception e) {
            logger.error("上传升级包失败: {}", e.getMessage(), e);
            // 如果需要回退，删除已上传的文件和临时目录
            if (needRollback) {
                logger.info("开始回退操作...");
                // 删除MinIO中的文件
                if (fileInfo != null) {
                    try {
                        fileStorageService.delete(fileInfo);
                        logger.info("回退删除MinIO文件成功: {}", fileInfo.getUrl());
                    } catch (Exception ex) {
                        logger.error("回退删除MinIO文件失败: {}", fileInfo.getUrl(), ex);
                    }
                }
                // 删除临时目录
                if (tempDir != null) {
                    File tempDirFile = new File(tempDir);
                    if (tempDirFile.exists()) {
                        try {
                            FileUtils.deleteDirectory(tempDirFile);
                            logger.info("回退删除临时目录成功: {}", tempDir);
                        } catch (Exception ex) {
                            logger.error("回退删除临时目录失败: {}", tempDir, ex);
                        }
                    }
                }
                logger.info("回退操作完成");
            }

            // 根据异常类型提供更友好的错误信息
            String errorMessage = e.getMessage();
            if (e instanceof BusinessException) {
                throw (BusinessException) e;
            } else if (e instanceof java.io.IOException) {
                throw new BusinessException("文件操作失败: " + errorMessage);
            } else {
                throw new BusinessException("上传升级包失败: " + errorMessage);
            }
        }
    }

    /**
     * 获取升级历史日志列表
     *
     * @return 升级历史日志列表
     */
    public List<UpgradeLog> getUpgradeLogs() {
        try {
            TypeReference<Map<String, UpgradeLog>> typeRef = new TypeReference<Map<String, UpgradeLog>>() {
            };
            Map<String, UpgradeLog> logsMap = JsonFileUtils.loadMapFromJson(upgradeLogsFile, typeRef);
            List<UpgradeLog> logsList = new ArrayList<>(logsMap.values());

            // 按升级时间倒序排序
            logsList.sort((a, b) -> b.getUpgradeTime().compareTo(a.getUpgradeTime()));

            return logsList;
        } catch (IOException e) {
            logger.error("获取升级历史日志列表失败", e);
            throw new BusinessException("获取升级历史日志列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取单个升级历史日志详情
     *
     * @param id 升级日志ID
     * @return 升级历史日志
     */
    public UpgradeLog getUpgradeLog(String id) {
        try {
            TypeReference<Map<String, UpgradeLog>> typeRef = new TypeReference<Map<String, UpgradeLog>>() {
            };
            Map<String, UpgradeLog> logsMap = JsonFileUtils.loadMapFromJson(upgradeLogsFile, typeRef);
            return logsMap.get(id);
        } catch (IOException e) {
            logger.error("获取升级历史日志详情失败", e);
            throw new BusinessException("获取升级历史日志详情失败: " + e.getMessage());
        }
    }

    /**
     * 保存升级历史日志
     *
     * @param log 升级日志
     */
    private void saveUpgradeLog(UpgradeLog log) {
        try {
            TypeReference<Map<String, UpgradeLog>> typeRef = new TypeReference<Map<String, UpgradeLog>>() {
            };
            Map<String, UpgradeLog> logsMap = JsonFileUtils.loadMapFromJson(upgradeLogsFile, typeRef);

            logsMap.put(log.getId(), log);

            JsonFileUtils.saveMapToJson(upgradeLogsFile, logsMap);
            logger.info("保存升级历史日志成功: {}", log.getId());
        } catch (IOException e) {
            logger.error("保存升级历史日志失败", e);
            throw new BusinessException("保存升级历史日志失败: " + e.getMessage());
        }
    }

    /**
     * 按文件回退到上一个版本
     *
     * @param fileName 需要回退的文件名，例如"icm-xxx"
     * @throws BusinessException 如果回退失败
     */
    public void rollbackUpgradeByFile(String fileName) {
        logger.info("开始执行文件回退，文件名: {}", fileName);

        if (fileName == null || fileName.trim().isEmpty()) {
            throw new BusinessException("文件名不能为空");
        }

        // 获取当前版本
        UpgradePackage currentPackage = getCurrentUpgradePackage();
        if (currentPackage == null) {
            throw new BusinessException("无法获取当前版本信息");
        }

        // 查找当前升级包中是否包含此文件
        boolean currentHasFile = false;
        if (currentPackage.getFiles() != null) {
            for (String key : currentPackage.getFiles().keySet()) {
                if (key.contains(fileName)) {
                    currentHasFile = true;
                    break;
                }
            }
        }

        if (!currentHasFile) {
            throw new BusinessException("当前版本不包含指定的文件: " + fileName);
        }

        // 查找包含该文件的上一个版本的升级包
        UpgradePackage previousPackage = findPreviousPackageWithFile(fileName);
        if (previousPackage == null) {
            throw new BusinessException("未找到包含文件 " + fileName + " 的上一个版本");
        }

        logger.info("准备将文件 {} 从当前版本 {} 回退到版本 {}", fileName, currentPackage.getVersion(), previousPackage.getVersion());

        try {
            // 创建回退日志
            UpgradeLog rollbackLog = new UpgradeLog();
            String logId = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmssSSS");
            rollbackLog.setId(logId);
            rollbackLog.setFileName(previousPackage.getName() + " (文件: " + fileName + ")");
            rollbackLog.setPackageId(previousPackage.getId());
            rollbackLog.setUpgradeTime(new Date());
            rollbackLog.setFromVersion(currentPackage.getVersion());
            rollbackLog.setToVersion(previousPackage.getVersion());
            rollbackLog.setRemark("文件回退: " + fileName);
            rollbackLog.setOperationType(1); // 设置操作类型为回退

            try {
                // 检查是否是ICM相关文件
                boolean isIcmFile = fileName.startsWith("icm-");

                // 执行实际回退操作，使用与升级相同的逻辑，但仅针对特定文件
                if (isIcmFile) {
                    execUpdateForIcmFile(previousPackage, fileName);
                } else {
                    execUpdateForFile(previousPackage, fileName);
                }

                // 回退成功，更新日志
                rollbackLog.setResult(0); // 成功
                saveUpgradeLog(rollbackLog);

                // 保存当前版本信息，可用于后续回退
                Map<String, String> filePathMap = new HashMap<>();
                if (currentPackage.getFiles() != null) {
                    for (Map.Entry<String, String> entry : currentPackage.getFiles().entrySet()) {
                        if (entry.getKey().contains(fileName)) {
                            filePathMap.put(entry.getKey(), entry.getValue());
                        }
                    }
                }

                // 更新前一个版本信息
                JsonFileUtils.saveObjectToJson(previousUpgradeFile, currentPackage);

                logger.info("文件回退完成: 文件 {} 从版本 {} 回退到版本 {}", fileName, currentPackage.getVersion(),
                        previousPackage.getVersion());
                if (isIcmFile) {
                    // 重启服务器
                    Thread.sleep(10000);
                    ExecuteUtils.executeCommand("sudo", "reboot");
                }
            } catch (Exception e) {
                logger.error("文件回退失败", e);

                // 回退失败，更新日志
                rollbackLog.setResult(1); // 失败
                rollbackLog.setErrorMessage(e.getMessage());
                saveUpgradeLog(rollbackLog);

                throw e;
            }
        } catch (Exception e) {
            logger.error("执行文件回退失败", e);
            throw new BusinessException("执行文件回退失败: " + e.getMessage());
        }
    }
}